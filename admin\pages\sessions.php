<?php
/**
 * VedMG ClassRoom Class Sessions Page
 * 
 * This page handles class session management functionality.
 * Allows viewing, scheduling, and managing Google Meet sessions for courses.
 * 
 * @package VedMG_ClassRoom
 * <AUTHOR>
 * @version 1.0
 */

// Prevent direct access to this file
if (!defined('ABSPATH')) {
    exit('Direct access denied.');
}

// Include database helper
require_once VEDMG_CLASSROOM_PLUGIN_DIR . 'database/helper.php';

// Log page access
vedmg_log_admin_action('Viewed class sessions page');

// Handle pagination and filtering parameters
$current_page = isset($_GET['paged']) ? max(1, intval($_GET['paged'])) : 1;
$per_page = isset($_GET['per_page']) ? max(10, min(100, intval($_GET['per_page']))) : 10;
$course_filter = isset($_GET['course_id']) ? intval($_GET['course_id']) : '';
$status_filter = isset($_GET['status']) ? sanitize_text_field($_GET['status']) : '';

// Get paginated data from database with auto-status update
$session_data = VedMG_ClassRoom_Database_Helper::get_class_sessions_with_auto_update($current_page, $per_page, $course_filter, $status_filter);
$sessions = $session_data['sessions'];
$total_count = $session_data['total_count'];
$total_pages = $session_data['total_pages'];

// Get classroom options for dropdowns
$classroom_options = VedMG_ClassRoom_Database_Helper::get_classroom_options();

// Get all courses for session editing
$courses_data = VedMG_ClassRoom_Database_Helper::get_courses(1, 100); // Get first 100 courses
$all_courses = $courses_data['courses'];

// Calculate pagination info
$start_item = (($current_page - 1) * $per_page) + 1;
$end_item = min($current_page * $per_page, $total_count);

// Log filtering if active
if ($course_filter || $status_filter) {
    vedmg_log_info('ADMIN', 'Filtering sessions - Course: ' . $course_filter . ', Status: ' . $status_filter);
}
?>

<div class="vedmg-classroom-admin">
    <!-- Page Header -->
    <div class="vedmg-classroom-header">
        <h1>Class Sessions</h1>
        <p>View and manage scheduled Google Meet sessions for your courses</p>
    </div>
    
    <!-- Session Management Controls -->
    <div class="vedmg-classroom-section">
        <div class="vedmg-section-header">
            <h2>Session Management</h2>
            <div class="vedmg-section-actions">
                <button class="vedmg-classroom-btn" id="schedule-session">
                    <span class="vedmg-classroom-spinner"></span>
                    Schedule New Session
                </button>
                <button class="vedmg-classroom-btn vedmg-classroom-btn-secondary" id="refresh-sessions">
                    Refresh Sessions
                </button>
            </div>
        </div>
        
        <!-- Session Filters -->
        <form method="GET" class="vedmg-filter-controls">
            <input type="hidden" name="page" value="<?php echo esc_attr($_GET['page'] ?? ''); ?>">
            
            <div class="vedmg-filter-group">
                <label for="course_id">Filter by Course:</label>
                <select name="course_id" id="course_id" class="vedmg-filter-select">
                    <option value="">All Courses</option>
                    <?php foreach ($classroom_options as $classroom): ?>
                        <option value="<?php echo $classroom->course_id; ?>" <?php selected($course_filter, $classroom->course_id); ?>>
                            <?php echo esc_html($classroom->course_name); ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>
            
            <div class="vedmg-filter-group">
                <label for="status">Filter by Status:</label>
                <select name="status" id="status" class="vedmg-filter-select">
                    <option value="">All Statuses</option>
                    <option value="scheduled" <?php selected($status_filter, 'scheduled'); ?>>Scheduled</option>
                    <option value="active" <?php selected($status_filter, 'active'); ?>>In Progress</option>
                    <option value="completed" <?php selected($status_filter, 'completed'); ?>>Completed</option>
                    <option value="cancelled" <?php selected($status_filter, 'cancelled'); ?>>Cancelled</option>
                </select>
            </div>
            
            <div class="vedmg-filter-group">
                <label for="per_page">Items per page:</label>
                <select name="per_page" id="per_page" class="vedmg-filter-select">
                    <option value="10" <?php selected($per_page, 10); ?>>10</option>
                    <option value="25" <?php selected($per_page, 25); ?>>25</option>
                    <option value="50" <?php selected($per_page, 50); ?>>50</option>
                    <option value="100" <?php selected($per_page, 100); ?>>100</option>
                </select>
            </div>
            
            <div class="vedmg-filter-group">
                <button type="submit" class="vedmg-classroom-btn">Apply Filters</button>
                <a href="<?php echo admin_url('admin.php?page=' . esc_attr($_GET['page'] ?? '')); ?>" 
                   class="vedmg-classroom-btn vedmg-classroom-btn-secondary">Clear Filters</a>
            </div>
        </form>
        
        <!-- Session Summary -->
        <div class="vedmg-enrollment-summary">
            <span>Total: <strong><?php echo $total_count; ?></strong></span>
            <span>Showing: <strong><?php echo $start_item; ?>-<?php echo $end_item; ?></strong></span>
            <span>Page: <strong><?php echo $current_page; ?> of <?php echo $total_pages; ?></strong></span>
        </div>
    </div>
    
    <!-- Featured Sessions -->
    <div class="vedmg-classroom-section">
        <h2>Featured Sessions</h2>
        <div class="vedmg-featured-sessions">
            <?php 
            // Get featured sessions using optimized database query
            $featured_sessions = VedMG_ClassRoom_Database_Helper::get_featured_sessions_only(3);
            
            if (!empty($featured_sessions)): 
            ?>
                <?php foreach ($featured_sessions as $session): ?>
                    <div class="vedmg-session-card" data-session-id="<?php echo $session->session_id; ?>">
                        <div class="vedmg-session-header">
                            <h4 class="vedmg-session-title"><?php echo esc_html($session->session_title); ?></h4>
                            <span class="vedmg-session-time">
                                <span class="vedmg-session-created">Created: <?php echo VedMG_ClassRoom_Database_Helper::format_created_date($session->created_date); ?></span>
                            </span>
                        </div>
                        <div class="vedmg-session-details">
                            <p><strong>Course:</strong> <?php echo esc_html($session->course_name ?: 'Unknown Course'); ?></p>
                            <p><strong>Instructor:</strong> <?php echo esc_html($session->instructor_name ?: 'Unknown'); ?></p>
                            <p><strong>Status:</strong> <?php echo VedMG_ClassRoom_Database_Helper::format_session_status($session->session_status); ?></p>
                        </div>
                        <div class="vedmg-session-actions">
                            <?php if (!empty($session->google_meet_link)): ?>
                                <a href="<?php echo esc_url($session->google_meet_link); ?>" 
                                   class="vedmg-classroom-btn" target="_blank">Join</a>
                            <?php endif; ?>
                            <button class="vedmg-classroom-btn vedmg-classroom-btn-danger vedmg-remove-featured-btn" 
                                    data-session-id="<?php echo $session->session_id; ?>">Remove Featured</button>
                        </div>
                    </div>
                <?php endforeach; ?>
            <?php else: ?>
                <div class="vedmg-empty-state">
                    <p>No featured sessions available.</p>
                    <p><small>Use the "Feature This Session" button in the sessions table below to feature sessions.</small></p>
                </div>
            <?php endif; ?>
        </div>
    </div>
    
    <!-- All Class Sessions -->
    <div class="vedmg-classroom-section">
        <div class="vedmg-section-header">
            <h2>All Class Sessions</h2>
            <div class="vedmg-session-summary">
                <span>Total: <strong><?php echo $total_count; ?></strong></span>
                <span>Scheduled: <strong id="scheduled-count">0</strong></span>
                <span>Completed: <strong id="completed-count">0</strong></span>
            </div>
        </div>
        
        <!-- Bulk Actions -->
        <div class="vedmg-bulk-actions">
            <select id="session-bulk-action-select">
                <option value="">Bulk Actions</option>
                <option value="cancel">Cancel Selected</option>
                <option value="reschedule">Reschedule Selected</option>
                <option value="delete">Delete Selected</option>
            </select>
            <button class="vedmg-classroom-btn vedmg-classroom-btn-secondary" id="apply-session-bulk-action">Apply</button>
        </div>
        
        <!-- Class Sessions Table -->
        <table class="vedmg-classroom-table">
            <thead>
                <tr>
                    <th><input type="checkbox" id="select-all-sessions"></th>
                    <th>Session Title</th>
                    <th>Course</th>
                    <th>Instructor</th>
                    <th>Created At</th>
                    <th>Status</th>
                    <th>Session Details</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                <?php if (!empty($sessions)): ?>
                    <?php foreach ($sessions as $session):
                        // Calculate session timing details
                        $session_datetime = $session->scheduled_date . ' ' . $session->start_time;
                        $end_datetime = $session->scheduled_date . ' ' . $session->end_time;
                        $start_timestamp = strtotime($session_datetime);
                        $end_timestamp = strtotime($end_datetime);
                        $current_timestamp = current_time('timestamp');

                        // Determine if session is expired/completed
                        $is_expired = $current_timestamp > $end_timestamp;
                        $is_ongoing = $current_timestamp >= $start_timestamp && $current_timestamp <= $end_timestamp;
                        $is_upcoming = $current_timestamp < $start_timestamp;

                        // Calculate time remaining or elapsed
                        if ($is_upcoming) {
                            $time_diff = $start_timestamp - $current_timestamp;
                            $time_status = 'Starts in ' . human_time_diff($current_timestamp, $start_timestamp);
                        } elseif ($is_ongoing) {
                            $time_diff = $end_timestamp - $current_timestamp;
                            $time_status = 'Ends in ' . human_time_diff($current_timestamp, $end_timestamp);
                        } else {
                            $time_diff = $current_timestamp - $end_timestamp;
                            $time_status = 'Ended ' . human_time_diff($end_timestamp, $current_timestamp) . ' ago';
                        }

                        // Calculate session duration
                        $duration_minutes = ($end_timestamp - $start_timestamp) / 60;
                        $duration_text = $duration_minutes >= 60 ?
                            floor($duration_minutes / 60) . 'h ' . ($duration_minutes % 60) . 'm' :
                            $duration_minutes . ' minutes';

                        // Row classes for visual state
                        $row_classes = ['real-data-row', 'session-row', 'session-status-' . $session->session_status];
                        if ($is_expired && $session->session_status !== 'completed') {
                            $row_classes[] = 'session-expired';
                        }
                        if ($is_ongoing) {
                            $row_classes[] = 'session-ongoing';
                        }
                    ?>
                        <tr class="<?php echo implode(' ', $row_classes); ?>"
                            data-session-id="<?php echo $session->session_id; ?>"
                            data-course-id="<?php echo $session->course_id; ?>"
                            data-session-status="<?php echo $session->session_status; ?>"
                            data-is-expired="<?php echo $is_expired ? '1' : '0'; ?>"
                            data-is-ongoing="<?php echo $is_ongoing ? '1' : '0'; ?>">
                            <td><input type="checkbox" class="session-checkbox" value="<?php echo $session->session_id; ?>"></td>
                            <td>
                                <strong><?php echo esc_html($session->session_title); ?></strong>
                                <div class="session-meta">
                                    <small class="session-source">
                                        <?php if (strpos($session->session_title, 'Schedule Lab') !== false): ?>
                                            📅 From Enrollments
                                        <?php else: ?>
                                            📚 From Courses
                                        <?php endif; ?>
                                    </small>
                                </div>
                            </td>
                            <td>
                                <a href="<?php echo admin_url('admin.php?page=vedmg-classroom-courses&course_id=' . $session->course_id); ?>"
                                   class="course-link"><?php echo esc_html($session->course_name ?: 'Unknown Course'); ?></a>
                            </td>
                            <td>
                                <a href="<?php echo admin_url('admin.php?page=vedmg-classroom-enrollments&instructor=' . urlencode($session->instructor_name)); ?>"
                                   class="instructor-link"><?php echo esc_html($session->instructor_name ?: 'Unknown'); ?></a>
                            </td>
                            <td>
                                <span class="vedmg-session-created-date">
                                    <?php echo VedMG_ClassRoom_Database_Helper::format_created_date($session->created_date); ?>
                                </span>
                            </td>
                            <td>
                                <span class="vedmg-session-status" data-status="<?php echo $session->session_status; ?>">
                                    <?php echo VedMG_ClassRoom_Database_Helper::format_session_status($session->session_status); ?>
                                </span>
                                <?php if ($is_ongoing): ?>
                                    <span class="session-live-indicator">🔴 LIVE</span>
                                <?php endif; ?>
                            </td>
                            <td class="session-details-cell">
                                <div class="session-timing">
                                    <div class="session-datetime">
                                        <strong>📅 <?php echo date('M j, Y', $start_timestamp); ?></strong><br>
                                        <span class="time-range">
                                            ⏰ <?php echo date('g:i A', $start_timestamp); ?> - <?php echo date('g:i A', $end_timestamp); ?>
                                        </span>
                                    </div>
                                    <div class="session-duration">
                                        <small>Duration: <?php echo $duration_text; ?></small>
                                    </div>
                                    <div class="session-status-time <?php echo $is_expired ? 'expired' : ($is_ongoing ? 'ongoing' : 'upcoming'); ?>">
                                        <strong><?php echo $time_status; ?></strong>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <div class="vedmg-action-buttons">
                                    <button class="vedmg-classroom-btn vedmg-join-meet-btn <?php echo $is_expired ? 'disabled' : ''; ?>"
                                            data-session-id="<?php echo $session->session_id; ?>"
                                            data-course-id="<?php echo $session->course_id; ?>"
                                            <?php echo $is_expired ? 'disabled' : ''; ?>>
                                        <?php echo $is_ongoing ? '🔴 Join Live' : ($is_expired ? '⚫ Ended' : '📹 Join Meet'); ?>
                                    </button>



                                    <button class="vedmg-classroom-btn vedmg-view-session-details-btn"
                                            data-session-id="<?php echo $session->session_id; ?>">👁️ View Details</button>

                                    <?php if (empty($session->is_featured) || $session->is_featured == 0): ?>
                                        <button class="vedmg-classroom-btn vedmg-classroom-btn-primary vedmg-feature-session-btn"
                                                data-session-id="<?php echo $session->session_id; ?>"
                                                data-session-title="<?php echo esc_attr($session->session_title); ?>">⭐ Feature Session</button>
                                    <?php else: ?>
                                        <button class="vedmg-classroom-btn vedmg-classroom-btn-warning vedmg-unfeature-session-btn"
                                                data-session-id="<?php echo $session->session_id; ?>"
                                                data-session-title="<?php echo esc_attr($session->session_title); ?>">⭐ Remove Featured</button>
                                    <?php endif; ?>

                                    <button class="vedmg-classroom-btn vedmg-classroom-btn-danger vedmg-cancel-delete-session-btn"
                                            data-session-id="<?php echo $session->session_id; ?>"
                                            data-session-title="<?php echo esc_attr($session->session_title); ?>">🗑️ Cancel/Delete</button>
                                </div>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                <?php endif; ?>
                
                <?php if (empty($sessions)): ?>
                    <tr>
                        <td colspan="7" style="text-align: center; padding: 20px; color: #666;">
                            <em>No class sessions scheduled yet. Create courses and schedule sessions to see them here.</em>
                        </td>
                    </tr>
                <?php endif; ?>
            </tbody>
        </table>
    </div>
    
    <!-- Server-Side Pagination -->
    <?php if ($total_pages > 1): ?>
    <div class="vedmg-pagination">
        <div class="vedmg-pagination-info">
            Showing <?php echo $start_item; ?> to <?php echo $end_item; ?> of <?php echo $total_count; ?> sessions
        </div>
        <div class="vedmg-pagination-controls">
            <?php
            $base_url = admin_url('admin.php');
            $query_args = array_merge($_GET, array('page' => $_GET['page']));
            
            // First page
            if ($current_page > 1):
                $first_url = add_query_arg(array_merge($query_args, array('paged' => 1)), $base_url);
            ?>
                <a href="<?php echo esc_url($first_url); ?>" class="vedmg-pagination-btn">‹‹</a>
            <?php else: ?>
                <span class="vedmg-pagination-btn disabled">‹‹</span>
            <?php endif; ?>
            
            <?php
            // Previous page
            if ($current_page > 1):
                $prev_url = add_query_arg(array_merge($query_args, array('paged' => $current_page - 1)), $base_url);
            ?>
                <a href="<?php echo esc_url($prev_url); ?>" class="vedmg-pagination-btn">‹</a>
            <?php else: ?>
                <span class="vedmg-pagination-btn disabled">‹</span>
            <?php endif; ?>
            
            <div class="vedmg-pagination-numbers">
                <?php
                // Calculate page range
                $start_page = max(1, $current_page - 2);
                $end_page = min($total_pages, $current_page + 2);
                
                for ($i = $start_page; $i <= $end_page; $i++):
                    if ($i === $current_page):
                ?>
                    <span class="vedmg-pagination-btn active"><?php echo $i; ?></span>
                <?php else:
                    $page_url = add_query_arg(array_merge($query_args, array('paged' => $i)), $base_url);
                ?>
                    <a href="<?php echo esc_url($page_url); ?>" class="vedmg-pagination-btn"><?php echo $i; ?></a>
                <?php endif; endfor; ?>
            </div>
            
            <?php
            // Next page
            if ($current_page < $total_pages):
                $next_url = add_query_arg(array_merge($query_args, array('paged' => $current_page + 1)), $base_url);
            ?>
                <a href="<?php echo esc_url($next_url); ?>" class="vedmg-pagination-btn">›</a>
            <?php else: ?>
                <span class="vedmg-pagination-btn disabled">›</span>
            <?php endif; ?>
            
            <?php
            // Last page
            if ($current_page < $total_pages):
                $last_url = add_query_arg(array_merge($query_args, array('paged' => $total_pages)), $base_url);
            ?>
                <a href="<?php echo esc_url($last_url); ?>" class="vedmg-pagination-btn">››</a>
            <?php else: ?>
                <span class="vedmg-pagination-btn disabled">››</span>
            <?php endif; ?>
        </div>
    </div>
    <?php endif; ?>
</div>

<!-- Enhanced Session Details Modal -->
<div id="vedmg-session-details-modal" class="vedmg-modal" style="display: none;">
    <div class="vedmg-modal-content">
        <div class="vedmg-modal-header">
            <h3>📅 Session Details</h3>
            <span class="vedmg-modal-close" id="close-session-details">&times;</span>
        </div>
        <div class="vedmg-modal-body">
            <div id="session-details-content">
                <!-- Content will be populated by JavaScript -->
            </div>
            <div class="vedmg-form-actions">
                <button type="button" class="vedmg-classroom-btn vedmg-classroom-btn-secondary" id="close-session-details-btn">Close</button>
            </div>
        </div>
    </div>
</div>



<!-- Session Schedule Modal -->
<div id="vedmg-session-schedule-modal" class="vedmg-modal" style="display: none;">
    <div class="vedmg-modal-content">
        <div class="vedmg-modal-header">
            <h3>Schedule New Session</h3>
            <span class="vedmg-modal-close">&times;</span>
        </div>
        <div class="vedmg-modal-body">
            <form id="vedmg-session-schedule-form">
                <div class="vedmg-form-group">
                    <label for="schedule-session-title">Session Title:</label>
                    <input type="text" id="schedule-session-title" name="session_title" class="vedmg-form-control" required placeholder="e.g., Introduction to Mathematics">
                </div>
                
                <div class="vedmg-form-group">
                    <label for="schedule-session-course">Select Course:</label>
                    <select id="schedule-session-course" name="course_id" class="vedmg-form-control" required>
                        <option value="">Choose a course</option>
                        <?php foreach ($classroom_options as $classroom): ?>
                            <option value="<?php echo $classroom->course_id; ?>">
                                <?php echo esc_html($classroom->course_name); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                
                <div class="vedmg-form-group">
                    <label for="schedule-session-description">Description (Optional):</label>
                    <textarea id="schedule-session-description" name="session_description" class="vedmg-form-control" rows="3" placeholder="Brief description of what will be covered..."></textarea>
                </div>
                
                <div class="vedmg-form-row">
                    <div class="vedmg-form-group vedmg-form-half">
                        <label for="schedule-session-date">Date:</label>
                        <input type="date" id="schedule-session-date" name="scheduled_date" class="vedmg-form-control" required min="<?php echo date('Y-m-d'); ?>">
                    </div>
                    <div class="vedmg-form-group vedmg-form-half">
                        <label for="schedule-session-start-time">Start Time:</label>
                        <input type="time" id="schedule-session-start-time" name="start_time" class="vedmg-form-control" required>
                    </div>
                </div>
                
                <div class="vedmg-form-row">
                    <div class="vedmg-form-group vedmg-form-half">
                        <label for="schedule-session-duration">Duration:</label>
                        <select id="schedule-session-duration" name="duration" class="vedmg-form-control">
                            <option value="30">30 minutes</option>
                            <option value="45">45 minutes</option>
                            <option value="60" selected>1 hour</option>
                            <option value="90">1.5 hours</option>
                            <option value="120">2 hours</option>
                            <option value="180">3 hours</option>
                        </select>
                    </div>
                    <div class="vedmg-form-group vedmg-form-half">
                        <label for="schedule-session-end-time">End Time:</label>
                        <input type="time" id="schedule-session-end-time" name="end_time" class="vedmg-form-control" readonly>
                    </div>
                </div>
                
                <div class="vedmg-form-group">
                    <label>Meeting Options:</label>
                    <div class="vedmg-checkbox-group">
                        <label>
                            <input type="checkbox" id="schedule-auto-record" name="auto_record" value="1">
                            Auto-record session
                        </label>
                        <label>
                            <input type="checkbox" id="schedule-send-notifications" name="send_notifications" value="1" checked>
                            Send notifications to enrolled students
                        </label>
                        <label>
                            <input type="checkbox" id="schedule-generate-meet" name="generate_meet" value="1" checked>
                            Auto-generate Google Meet link
                        </label>
                    </div>
                </div>
                
                <div class="vedmg-form-actions">
                    <button type="button" class="vedmg-classroom-btn vedmg-classroom-btn-secondary" id="cancel-session-schedule">Cancel</button>
                    <button type="submit" class="vedmg-classroom-btn vedmg-classroom-btn-primary" id="save-session-schedule">
                        <span class="vedmg-classroom-spinner"></span>
                        Schedule Session
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<style>
/* Enhanced styling for sessions management */
.vedmg-session-card {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 15px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.vedmg-session-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.15);
    border-color: #0073aa;
}

.vedmg-session-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 15px;
}

.vedmg-session-header h4 {
    margin: 0;
    color: #333;
    font-size: 18px;
}

.vedmg-session-time {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 5px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
}

.vedmg-session-details {
    margin-bottom: 15px;
}

.vedmg-session-details p {
    margin: 5px 0;
    color: #666;
    font-size: 14px;
}

.vedmg-session-actions {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.vedmg-empty-state {
    text-align: center;
    padding: 40px 20px;
    background: #f8f9fa;
    border-radius: 8px;
    margin-top: 20px;
}

.vedmg-empty-state p {
    color: #666;
    font-size: 16px;
    margin-bottom: 20px;
}

/* Filter controls enhancement */
.vedmg-filter-controls {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 25px;
    padding: 20px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.vedmg-filter-group {
    display: flex;
    flex-direction: column;
}

.vedmg-filter-group label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 5px;
    font-size: 12px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.vedmg-filter-select,
.vedmg-filter-input {
    padding: 8px 12px;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    font-size: 14px;
    transition: border-color 0.3s ease;
}

.vedmg-filter-select:focus,
.vedmg-filter-input:focus {
    border-color: #0073aa;
    outline: none;
    box-shadow: 0 0 0 2px rgba(0,115,170,0.1);
}

/* Session status enhancement */
.vedmg-session-status {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.vedmg-status-pending {
    background: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

.vedmg-status-active {
    background: #d1ecf1;
    color: #0c5460;
    border: 1px solid #b8daff;
}

/* Session status-based row styling */
.session-status-completed {
    background-color: #f8f9fa !important;
    color: #6c757d !important;
    opacity: 0.7;
    filter: grayscale(100%);
}

.session-status-completed td {
    color: #6c757d !important;
}

.session-status-completed .vedmg-session-status {
    background: #e9ecef;
    color: #6c757d;
    border: 1px solid #dee2e6;
}

.session-status-completed .vedmg-classroom-btn {
    opacity: 0.6;
    background-color: #e9ecef !important;
    color: #6c757d !important;
    border-color: #dee2e6 !important;
}

.session-status-completed .vedmg-classroom-btn:hover {
    opacity: 0.8;
}

.session-status-ongoing {
    background-color: #fff3cd !important;
    border-left: 4px solid #ffc107;
}

.session-status-ongoing .vedmg-session-status {
    background: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.7; }
    100% { opacity: 1; }
}

.session-status-scheduled {
    background-color: #e7f3ff !important;
    border-left: 4px solid #0073aa;
}

.session-status-cancelled {
    background-color: #ffe6e6 !important;
    color: #721c24 !important;
    text-decoration: line-through;
    opacity: 0.6;
}

/* Checkbox group styling */
.vedmg-checkbox-group {
    display: flex;
    flex-direction: column;
    gap: 10px;
    margin-top: 8px;
}

.vedmg-checkbox-group label {
    display: flex;
    align-items: center;
    font-weight: normal;
    color: #495057;
    font-size: 14px;
    cursor: pointer;
}

.vedmg-checkbox-group input[type="checkbox"] {
    margin-right: 8px;
    transform: scale(1.1);
}

/* Modal Styles */
.vedmg-modal {
    position: fixed;
    z-index: 10000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
    overflow-y: auto;
}

.vedmg-modal-content {
    background-color: #fff;
    margin: 5% auto;
    padding: 0;
    border-radius: 8px;
    width: 90%;
    max-width: 600px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
    animation: modalFadeIn 0.3s ease;
    position: relative;
}

/* Management Modal Specific Styles */
.vedmg-management-modal {
    padding: 20px 0;
}

.vedmg-management-modal-content {
    max-width: 900px;
    max-height: 90vh;
    overflow-y: auto;
    margin: 2% auto;
}

.vedmg-management-modal-body {
    max-height: 70vh;
    overflow-y: auto;
    padding: 25px;
}

/* Management Grid */
.vedmg-management-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.vedmg-management-card {
    background: #f9f9f9;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 20px;
    text-align: center;
    transition: all 0.3s ease;
}

.vedmg-management-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    border-color: #0073aa;
}

.vedmg-management-card h4 {
    margin: 0 0 10px 0;
    color: #333;
    font-size: 16px;
}

.vedmg-management-card p {
    color: #666;
    font-size: 14px;
    margin: 0 0 15px 0;
    line-height: 1.5;
}

.vedmg-management-card .vedmg-classroom-btn {
    width: 100%;
    justify-content: center;
}

@keyframes modalFadeIn {
    from { opacity: 0; transform: translateY(-50px); }
    to { opacity: 1; transform: translateY(0); }
}

.vedmg-modal-header {
    padding: 20px 25px;
    border-bottom: 1px solid #ddd;
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: sticky;
    top: 0;
    background: #fff;
    z-index: 10;
}

.vedmg-modal-header h3 {
    margin: 0;
    color: #333;
}

.vedmg-modal-close {
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    color: #aaa;
    padding: 5px;
    line-height: 1;
    border: none;
    background: none;
    outline: none;
}

.vedmg-modal-close:hover {
    color: #000;
}

.vedmg-modal-body {
    padding: 25px;
}

.vedmg-form-group {
    margin-bottom: 20px;
}

.vedmg-form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #333;
}

.vedmg-form-control {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    transition: border-color 0.3s ease;
    box-sizing: border-box;
}

.vedmg-form-control:focus {
    border-color: #0073aa;
    outline: none;
    box-shadow: 0 0 0 2px rgba(0,115,170,0.1);
}

.vedmg-form-row {
    display: flex;
    gap: 15px;
}

.vedmg-form-half {
    flex: 1;
}

.vedmg-form-actions {
    margin-top: 30px;
    display: flex;
    gap: 10px;
    justify-content: flex-end;
}

.vedmg-form-group input[type="checkbox"] {
    width: auto;
    margin-right: 8px;
}

/* Responsive Design for Modals */
@media (max-width: 768px) {
    .vedmg-modal-content {
        width: 95%;
        margin: 2% auto;
    }
    
    .vedmg-management-modal-content {
        margin: 1% auto;
        max-height: 95vh;
    }
    
    .vedmg-management-modal-body {
        max-height: 80vh;
        padding: 15px;
    }
    
    .vedmg-management-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }
    
    .vedmg-management-card {
        padding: 15px;
    }
    
    .vedmg-modal-header {
        padding: 15px 20px;
    }
    
    .vedmg-form-row {
        flex-direction: column;
        gap: 0;
    }
    
    .vedmg-form-actions {
        flex-direction: column;
    }
}

@media (max-width: 480px) {
    .vedmg-modal-content {
        width: 98%;
        margin: 1% auto;
    }
    
    .vedmg-management-modal-content {
        max-height: 98vh;
    }
    
    .vedmg-modal-header h3 {
        font-size: 16px;
    }
    
    .vedmg-management-card h4 {
        font-size: 14px;
    }
    
    .vedmg-management-card p {
        font-size: 13px;
    }
}

/* Enhanced Session Row Styles */
.session-row.session-expired {
    background-color: #f8f9fa;
    opacity: 0.7;
}

.session-row.session-expired td {
    color: #6c757d;
}

.session-row.session-ongoing {
    background-color: #fff3cd;
    border-left: 4px solid #ffc107;
}

.session-row.session-ongoing .session-live-indicator {
    color: #dc3545;
    font-weight: bold;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

/* Session Details Cell */
.session-details-cell {
    min-width: 200px;
}

.session-timing {
    font-size: 12px;
    line-height: 1.4;
}

.session-datetime {
    margin-bottom: 5px;
}

.time-range {
    color: #666;
}

.session-duration {
    color: #888;
    margin-bottom: 5px;
}

.session-status-time {
    font-weight: bold;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 11px;
}

.session-status-time.upcoming {
    background-color: #e3f2fd;
    color: #1976d2;
}

.session-status-time.ongoing {
    background-color: #fff3e0;
    color: #f57c00;
}

.session-status-time.expired {
    background-color: #fafafa;
    color: #757575;
}

/* Enhanced Action Buttons */
.vedmg-action-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
}

.vedmg-action-buttons .vedmg-classroom-btn {
    font-size: 11px;
    padding: 4px 8px;
    white-space: nowrap;
}

.vedmg-classroom-btn.disabled {
    opacity: 0.5;
    cursor: not-allowed;
    background-color: #6c757d !important;
}

/* Course and Instructor Links */
.course-link, .instructor-link {
    color: #007cba;
    text-decoration: none;
}

.course-link:hover, .instructor-link:hover {
    text-decoration: underline;
}

/* Session Meta */
.session-meta {
    margin-top: 4px;
}

.session-source {
    color: #666;
    font-style: italic;
}

/* Session Details Modal Styles */
.session-details-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 20px;
    margin-bottom: 20px;
}

@media (min-width: 768px) {
    .session-details-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (min-width: 1024px) {
    .session-details-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

.detail-section {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    border-left: 4px solid #007cba;
}

.detail-section h4 {
    margin: 0 0 10px 0;
    color: #007cba;
    font-size: 14px;
    font-weight: 600;
}

.detail-section p {
    margin: 5px 0;
    font-size: 13px;
    line-height: 1.4;
}

.detail-section strong {
    color: #333;
}

.meet-link {
    color: #007cba;
    text-decoration: none;
    font-weight: 500;
}

.meet-link:hover {
    text-decoration: underline;
}

.status-upcoming {
    color: #1976d2;
    background-color: #e3f2fd;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 12px;
}

.status-ongoing {
    color: #f57c00;
    background-color: #fff3e0;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 12px;
    animation: pulse 2s infinite;
}

.status-expired {
    color: #757575;
    background-color: #fafafa;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 12px;
}

/* Form Help Text */
.form-help {
    display: block;
    margin-top: 5px;
    color: #666;
    font-style: italic;
}

/* Pagination Styles for Sessions */
.vedmg-pagination {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 25px;
    padding: 20px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.vedmg-pagination-info {
    color: #666;
    font-size: 14px;
}

.vedmg-pagination-controls {
    display: flex;
    align-items: center;
    gap: 5px;
}

.vedmg-pagination-btn {
    padding: 8px 12px;
    border: 1px solid #dee2e6;
    background: white;
    color: #495057;
    cursor: pointer;
    border-radius: 4px;
    transition: all 0.2s ease;
    font-size: 14px;
}

.vedmg-pagination-btn:hover:not(:disabled) {
    background: #e9ecef;
    border-color: #adb5bd;
}

.vedmg-pagination-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.vedmg-pagination-btn.active {
    background: #667eea;
    color: white;
    border-color: #667eea;
}

.vedmg-pagination-numbers {
    display: flex;
    gap: 2px;
}

.vedmg-pagination-size {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #666;
    font-size: 14px;
}

.vedmg-pagination-size select {
    padding: 5px 8px;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    background: white;
}

@media (max-width: 768px) {
    .vedmg-pagination {
        flex-direction: column;
        gap: 15px;
    }
    
    .vedmg-pagination-controls {
        order: -1;
    }
}
</style>

<script>
// Session management specific JavaScript
jQuery(document).ready(function($) {
    console.log('Class sessions page loaded');
    
    // Update session counts
    updateSessionCounts();
    
    function updateSessionCounts() {
        var scheduledCount = $('.vedmg-session-status[data-status="scheduled"]').length;
        var googleClassroomCount = $('.vedmg-session-status[data-status="google classroom"]').length;
        var completedCount = $('.vedmg-session-status[data-status="completed"]').length;
        
        $('#scheduled-count').text(scheduledCount + googleClassroomCount); // Include Google Classroom in scheduled count
        $('#completed-count').text(completedCount);
    }
    
    // Any session-specific JavaScript will be handled by sessions.js
});
</script>
