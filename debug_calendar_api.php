<?php
/**
 * Debug Calendar API Call
 * 
 * This script debugs the exact API call being made to delete calendar
 * and compares it with the working documentation format
 * 
 * Run with: C:\xampp\php\php.exe "c:\xampp\htdocs\paylearn\wp-content\plugins\VedMG-ClassRoom\debug_calendar_api.php"
 */

// Load WordPress
$wp_load_path = dirname(dirname(dirname(dirname(__FILE__)))) . '/wp-load.php';
if (!file_exists($wp_load_path)) {
    die("❌ WordPress not found. Please check the path.\n");
}

require_once $wp_load_path;

echo "🔍 DEBUGGING CALENDAR API CALL\n";
echo str_repeat("=", 40) . "\n\n";

// Test data from the documentation
$test_calendar_id = '<EMAIL>';
$test_instructor_email = '<EMAIL>';

echo "📋 Test Data:\n";
echo "   Calendar ID: $test_calendar_id\n";
echo "   Instructor Email: $test_instructor_email\n\n";

echo "📖 DOCUMENTATION FORMAT:\n";
echo "   Method: POST\n";
echo "   URL: https://gclassroom-839391304260.us-central1.run.app/delete_calendar\n";
echo "   Headers: x-api-key: G\$\$gle@VedMG!@#\n";
echo "   Body: {\"instructor_name\": \"<EMAIL>\", \"calendar_id\": \"<EMAIL>\"}\n\n";

echo "🧪 TEST 1: Direct API Call (Documentation Format)\n";
echo str_repeat("-", 30) . "\n";

$api_url = 'https://gclassroom-839391304260.us-central1.run.app/delete_calendar';
$api_key = 'G$$gle@VedMG!@#';

$post_data = json_encode(array(
    'instructor_name' => $test_instructor_email,
    'calendar_id' => $test_calendar_id
));

echo "📤 Request Details:\n";
echo "   URL: $api_url\n";
echo "   Headers: Content-Type: application/json, x-api-key: " . substr($api_key, 0, 5) . "***\n";
echo "   Body: $post_data\n\n";

$response = wp_remote_post($api_url, array(
    'timeout' => 30,
    'headers' => array(
        'Content-Type' => 'application/json',
        'x-api-key' => $api_key
    ),
    'body' => $post_data
));

if (is_wp_error($response)) {
    echo "❌ WordPress Error: " . $response->get_error_message() . "\n\n";
} else {
    $response_code = wp_remote_retrieve_response_code($response);
    $response_body = wp_remote_retrieve_body($response);
    
    echo "📥 Response:\n";
    echo "   Status Code: $response_code\n";
    echo "   Body: $response_body\n\n";
    
    if ($response_code === 200) {
        echo "✅ Direct API call SUCCESSFUL!\n\n";
    } else {
        echo "❌ Direct API call FAILED!\n\n";
    }
}

echo "🧪 TEST 2: API Class Call\n";
echo str_repeat("-", 20) . "\n";

// Load API class
require_once __DIR__ . '/api/api.php';

echo "📤 Using VedMG_ClassRoom_API::call_google_classroom_api()\n";
echo "   Endpoint: delete_calendar\n";
echo "   Data: " . json_encode(array(
    'instructor_name' => $test_instructor_email,
    'calendar_id' => $test_calendar_id
)) . "\n\n";

$api_response = VedMG_ClassRoom_API::call_google_classroom_api('delete_calendar', array(
    'instructor_name' => $test_instructor_email,
    'calendar_id' => $test_calendar_id
));

echo "📥 API Class Response:\n";
print_r($api_response);
echo "\n";

if (isset($api_response['success']) && $api_response['success']) {
    echo "✅ API Class call indicates SUCCESS\n";
} elseif (isset($api_response['message']) && strpos($api_response['message'], 'deleted successfully') !== false) {
    echo "✅ API Class call indicates SUCCESS (message contains 'deleted successfully')\n";
} else {
    echo "❌ API Class call indicates FAILURE\n";
    if (isset($api_response['error'])) {
        echo "   Error: {$api_response['error']}\n";
    }
}

echo "\n🔍 COMPARISON:\n";
echo str_repeat("=", 20) . "\n";
echo "Direct API call vs API Class call comparison completed.\n";
echo "Check the responses above to identify any differences.\n\n";

echo "💡 DEBUGGING TIPS:\n";
echo "1. Check if the calendar ID exists in Google Calendar\n";
echo "2. Verify the instructor email has access to the calendar\n";
echo "3. Ensure the API key is correct\n";
echo "4. Check if the calendar was already deleted\n";
echo "5. Verify the API endpoint is working\n\n";

echo "✅ Debug completed!\n";
?>
