<?php
/**
 * VedMG ClassRoom Admin Interface
 * 
 * This file handles all admin-related functionality for the plugin.
 * It creates admin menus, pages, and handles admin actions.
 * 
 * @package VedMG_ClassRoom
 * <AUTHOR>
 * @version 1.0
 */

// Prevent direct access to this file
if (!defined('ABSPATH')) {
    exit('Direct access denied.');
}

/**
 * VedMG ClassRoom Admin Class
 * 
 * Handles all admin interface functionality
 */
class VedMG_ClassRoom_Admin {
    
    /**
     * @var string Admin page slug
     */
    private static $admin_page_slug = 'vedmg-classroom';
    
    /**
     * Initialize admin functionality
     * Sets up admin hooks and actions
     */
    public static function init() {
        // Add admin menu
        add_action('admin_menu', array(__CLASS__, 'add_admin_menu'));
        
        // Initialize admin settings
        add_action('admin_init', array(__CLASS__, 'init_settings'));
        
        // Hook into settings update
        add_action('update_option_vedmg_classroom_debug_enabled', array(__CLASS__, 'on_debug_option_update'), 10, 2);
        add_action('update_option_vedmg_classroom_console_logging_enabled', array(__CLASS__, 'on_console_logging_option_update'), 10, 2);
        
        // Enqueue admin scripts and styles
        add_action('admin_enqueue_scripts', array(__CLASS__, 'enqueue_admin_assets'));
        
        // Handle admin actions
        add_action('wp_ajax_vedmg_classroom_action', array(__CLASS__, 'handle_ajax_actions'));
        
        // Handle schedule session AJAX
        add_action('wp_ajax_vedmg_schedule_session', array(__CLASS__, 'handle_schedule_session_ajax'));
        
        // Handle session details AJAX
        add_action('wp_ajax_vedmg_get_session_details', array(__CLASS__, 'handle_get_session_details_ajax'));

        // Handle feature/unfeature session AJAX
        add_action('wp_ajax_vedmg_feature_session', array(__CLASS__, 'handle_feature_session_ajax'));
        add_action('wp_ajax_vedmg_unfeature_session', array(__CLASS__, 'handle_unfeature_session_ajax'));

        // Handle get course meeting link AJAX
        add_action('wp_ajax_vedmg_get_course_meeting_link', array(__CLASS__, 'handle_get_course_meeting_link_ajax'));
        
        // Handle instructor sync AJAX
        add_action('wp_ajax_vedmg_sync_instructors', array(__CLASS__, 'handle_sync_instructors_ajax'));
        add_action('wp_ajax_vedmg_sync_google_classroom', array(__CLASS__, 'handle_sync_google_classroom_ajax'));
        add_action('wp_ajax_vedmg_verify_instructor_sync_status', array(__CLASS__, 'handle_verify_instructor_sync_status_ajax'));
        add_action('wp_ajax_vedmg_get_all_instructors', array(__CLASS__, 'handle_get_all_instructors_ajax'));
        add_action('wp_ajax_vedmg_cleanup_instructor_sync', array(__CLASS__, 'handle_cleanup_instructor_sync_ajax'));

        // Handle course data AJAX
        add_action('wp_ajax_vedmg_get_course_data', array(__CLASS__, 'handle_get_course_data_ajax'));

        // Handle schedule lab course data AJAX
        add_action('wp_ajax_vedmg_get_schedule_course_data', array(__CLASS__, 'handle_get_schedule_course_data_ajax'));
        
        // Log admin initialization
        vedmg_log_info('ADMIN', 'Admin interface initialized');
    }

    /**
     * Handle get course data AJAX request
     * Returns course data including calendar_id and classroom link information
     */
    public static function handle_get_course_data_ajax() {
        // Verify nonce for security
        if (!wp_verify_nonce($_POST['nonce'], 'vedmg_classroom_nonce')) {
            wp_send_json_error('Security check failed');
            return;
        }

        // Check user permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
            return;
        }

        $course_id = intval($_POST['course_id']);

        if (empty($course_id)) {
            wp_send_json_error('Course ID is required');
            return;
        }

        try {
            global $wpdb;
            $courses_table = $wpdb->prefix . 'vedmg_courses';

            // Get course data
            $course = $wpdb->get_row($wpdb->prepare(
                "SELECT course_id, course_name, course_description, instructor_id, instructor_name, instructor_email,
                        google_classroom_id, calendar_id, google_classroom_link, class_join_link, classroom_status, course_status
                 FROM $courses_table
                 WHERE course_id = %d",
                $course_id
            ));

            if (!$course) {
                wp_send_json_error('Course not found');
                return;
            }

            // Prepare response data
            $response_data = array(
                'course_id' => $course->course_id,
                'course_name' => $course->course_name,
                'course_description' => $course->course_description,
                'instructor_id' => $course->instructor_id,
                'instructor_name' => $course->instructor_name,
                'instructor_email' => $course->instructor_email,
                'google_classroom_id' => $course->google_classroom_id,
                'calendar_id' => $course->calendar_id,
                'google_classroom_link' => $course->google_classroom_link,
                'class_join_link' => $course->class_join_link,
                'classroom_status' => $course->classroom_status,
                'course_status' => $course->course_status,
                'has_api_classroom' => !empty($course->google_classroom_id),
                'has_classroom_link' => !empty($course->google_classroom_link),
                'has_class_join_link' => !empty($course->class_join_link),
                'has_any_link' => !empty($course->google_classroom_link) || !empty($course->class_join_link),
                'is_api_generated_link' => !empty($course->google_classroom_link) &&
                                         strpos($course->google_classroom_link, 'classroom.google.com') !== false
            );

            vedmg_log_info('ADMIN', 'Course data retrieved successfully', array('course_id' => $course_id));

            wp_send_json_success($response_data);

        } catch (Exception $e) {
            vedmg_log_error('ADMIN', 'Failed to get course data', array(
                'course_id' => $course_id,
                'error' => $e->getMessage()
            ));

            wp_send_json_error('Failed to retrieve course data: ' . $e->getMessage());
        }
    }
    
    /**
     * Handle debug option update
     * Logs when debug setting is changed
     */
    public static function on_debug_option_update($old_value, $new_value) {
        vedmg_log_admin_action('Settings updated', 'Debug enabled: ' . ($new_value ? 'Yes' : 'No'));
    }
    
    /**
     * Handle console logging option update
     * Logs when console logging setting is changed
     */
    public static function on_console_logging_option_update($old_value, $new_value) {
        vedmg_log_admin_action('Settings updated', 'Console logging enabled: ' . ($new_value ? 'Yes' : 'No'));
    }
    
    /**
     * Initialize settings
     * Register settings using WordPress Settings API
     */
    public static function init_settings() {
        // Register settings
        register_setting(
            'vedmg_classroom_settings',           // Option group
            'vedmg_classroom_debug_enabled',      // Option name
            array(
                'type' => 'boolean',
                'default' => false,
                'sanitize_callback' => array(__CLASS__, 'sanitize_debug_option')
            )
        );
        
        register_setting(
            'vedmg_classroom_settings',           // Option group
            'vedmg_classroom_console_logging_enabled', // Option name
            array(
                'type' => 'boolean',
                'default' => false,
                'sanitize_callback' => array(__CLASS__, 'sanitize_console_logging_option')
            )
        );
        
        // Add settings section
        add_settings_section(
            'vedmg_classroom_general_section',    // Section ID
            'General Settings',                   // Section title
            array(__CLASS__, 'settings_section_callback'), // Callback
            'vedmg_classroom_settings'            // Page slug
        );
        
        // Add settings field for debug logging
        add_settings_field(
            'vedmg_classroom_debug_enabled',      // Field ID
            'Enable Debug Logging',               // Field title
            array(__CLASS__, 'debug_field_callback'), // Callback
            'vedmg_classroom_settings',           // Page slug
            'vedmg_classroom_general_section'     // Section
        );
        
        // Add settings field for console logging
        add_settings_field(
            'vedmg_classroom_console_logging_enabled', // Field ID
            'Enable Console Logging',             // Field title
            array(__CLASS__, 'console_logging_field_callback'), // Callback
            'vedmg_classroom_settings',           // Page slug
            'vedmg_classroom_general_section'     // Section
        );
        
        // Log settings initialization
        vedmg_log_info('ADMIN', 'Settings initialized');
    }
    
    /**
     * Settings section callback
     */
    public static function settings_section_callback() {
        echo '<p>Configure plugin settings and manage debug options.</p>';
    }
    
    /**
     * Debug field callback
     */
    public static function debug_field_callback() {
        $debug_enabled = get_option('vedmg_classroom_debug_enabled', false);
        echo '<input type="checkbox" name="vedmg_classroom_debug_enabled" id="vedmg_classroom_debug_enabled" value="1" ' . checked(1, $debug_enabled, false) . ' />';
        echo '<p class="description">Enable this to log all plugin activities for debugging purposes.</p>';
    }
    
    /**
     * Console logging field callback
     */
    public static function console_logging_field_callback() {
        $console_logging_enabled = get_option('vedmg_classroom_console_logging_enabled', false);
        echo '<input type="checkbox" name="vedmg_classroom_console_logging_enabled" id="vedmg_classroom_console_logging_enabled" value="1" ' . checked(1, $console_logging_enabled, false) . ' />';
        echo '<p class="description">Enable this to show console.log() messages in browser developer console for debugging JavaScript functionality.</p>';
    }
    
    /**
     * Sanitize debug option
     */
    public static function sanitize_debug_option($value) {
        return $value ? 1 : 0;
    }
    
    /**
     * Sanitize console logging option
     */
    public static function sanitize_console_logging_option($value) {
        return $value ? 1 : 0;
    }
    
    /**
     * Add admin menu
     * Creates the main admin menu for the plugin
     */
    public static function add_admin_menu() {
        // Add main menu page
        add_menu_page(
            'VedMG ClassRoom',          // Page title
            'VedMG ClassRoom',          // Menu title
            'manage_options',           // Capability
            self::$admin_page_slug,     // Menu slug
            array(__CLASS__, 'display_admin_page'), // Callback function
            'dashicons-welcome-learn-more',         // Icon
            30                          // Position
        );
        
        // Add submenu pages
        add_submenu_page(
            self::$admin_page_slug,     // Parent slug
            'Dashboard',                // Page title
            'Dashboard',                // Menu title
            'manage_options',           // Capability
            self::$admin_page_slug,     // Menu slug
            array(__CLASS__, 'display_admin_page') // Callback
        );
        
        add_submenu_page(
            self::$admin_page_slug,     // Parent slug
            'Course Management',        // Page title
            'Course Management',        // Menu title
            'manage_options',           // Capability
            self::$admin_page_slug . '-courses', // Menu slug
            array(__CLASS__, 'display_courses_page') // Callback
        );
        
        add_submenu_page(
            self::$admin_page_slug,     // Parent slug
            'Student Enrollments',      // Page title
            'Student Enrollments',      // Menu title
            'manage_options',           // Capability
            self::$admin_page_slug . '-enrollments', // Menu slug
            array(__CLASS__, 'display_enrollments_page') // Callback
        );
        
        add_submenu_page(
            self::$admin_page_slug,     // Parent slug
            'Class Sessions',           // Page title
            'Class Sessions',           // Menu title
            'manage_options',           // Capability
            self::$admin_page_slug . '-sessions', // Menu slug
            array(__CLASS__, 'display_sessions_page') // Callback
        );
        
        add_submenu_page(
            self::$admin_page_slug,     // Parent slug
            'Instructor Roster',        // Page title
            'Instructor Roster',        // Menu title
            'manage_options',           // Capability
            self::$admin_page_slug . '-instructors', // Menu slug
            array(__CLASS__, 'display_instructors_page') // Callback
        );
        
        add_submenu_page(
            self::$admin_page_slug,     // Parent slug
            'Instructor Sync',          // Page title
            'Instructor Sync',          // Menu title
            'manage_options',           // Capability
            self::$admin_page_slug . '-instructor-sync', // Menu slug
            array(__CLASS__, 'display_instructor_sync_page') // Callback
        );
        
        add_submenu_page(
            self::$admin_page_slug,     // Parent slug
            'Settings',                 // Page title
            'Settings',                 // Menu title
            'manage_options',           // Capability
            self::$admin_page_slug . '-settings', // Menu slug
            array(__CLASS__, 'display_settings_page') // Callback
        );
        
        // Log menu creation
        vedmg_log_info('ADMIN', 'Admin menu created');
    }
    
    /**
     * Display main admin page
     * Shows the main dashboard for the plugin
     */
    public static function display_admin_page() {
        // Log page access
        vedmg_log_admin_action('Accessed main admin page');
        
        // Include the main admin page template
        include VEDMG_CLASSROOM_PLUGIN_DIR . 'admin/pages/dashboard.php';
    }
    
    /**
     * Display settings page
     * Shows the settings page for the plugin
     */
    public static function display_settings_page() {
        // Log page access
        vedmg_log_admin_action('Accessed settings page');
        
        // Include the settings page template
        include VEDMG_CLASSROOM_PLUGIN_DIR . 'admin/pages/settings.php';
    }
    
    /**
     * Display courses page
     * Shows the course management page
     */
    public static function display_courses_page() {
        // Log page access
        vedmg_log_admin_action('Accessed courses page');
        
        // Include the courses page template
        include VEDMG_CLASSROOM_PLUGIN_DIR . 'admin/pages/courses.php';
    }
    
    /**
     * Display enrollments page
     * Shows the student enrollments page
     */
    public static function display_enrollments_page() {
        // Log page access
        vedmg_log_admin_action('Accessed enrollments page');
        
        // Include the enrollments page template
        include VEDMG_CLASSROOM_PLUGIN_DIR . 'admin/pages/enrollments.php';
    }
    
    /**
     * Display sessions page
     * Shows the class sessions page
     */
    public static function display_sessions_page() {
        // Log page access
        vedmg_log_admin_action('Accessed sessions page');
        
        // Include the sessions page template
        include VEDMG_CLASSROOM_PLUGIN_DIR . 'admin/pages/sessions.php';
    }
    
    /**
     * Display instructors page
     * Shows the instructor roster page
     */
    public static function display_instructors_page() {
        // Log page access
        vedmg_log_admin_action('Accessed instructors page');
        
        // Include the instructors page template
        include VEDMG_CLASSROOM_PLUGIN_DIR . 'admin/pages/instructors.php';
    }

    /**
     * Display instructor sync page
     * Shows the instructor sync management page
     */
    public static function display_instructor_sync_page() {
        // Log page access
        vedmg_log_admin_action('Accessed instructor sync page');
        
        // Include the instructor sync page template
        include VEDMG_CLASSROOM_PLUGIN_DIR . 'admin/pages/instructor_sync.php';
    }

    /**
     * Enqueue admin scripts and styles
     * Loads CSS and JS files for admin pages
     */
    public static function enqueue_admin_assets($hook) {
        // Only load on our admin pages
        if (strpos($hook, self::$admin_page_slug) === false) {
            return;
        }
        
        // Always enqueue main admin CSS
        wp_enqueue_style(
            'vedmg-classroom-admin',
            VEDMG_CLASSROOM_PLUGIN_URL . 'admin/css/AdminCSS.css',
            array(),
            VEDMG_CLASSROOM_VERSION
        );
        
        // Always enqueue main admin JavaScript
        wp_enqueue_script(
            'vedmg-classroom-admin',
            VEDMG_CLASSROOM_PLUGIN_URL . 'admin/js/AdminJS.js',
            array('jquery'),
            VEDMG_CLASSROOM_VERSION,
            true
        );
        
        // Determine which page we're on and load page-specific assets
        $page_type = self::get_current_admin_page($hook);
        
        if ($page_type) {
            // Enqueue page-specific CSS
            $css_file = VEDMG_CLASSROOM_PLUGIN_DIR . 'admin/css/' . $page_type . '.css';
            if (file_exists($css_file)) {
                wp_enqueue_style(
                    'vedmg-classroom-' . $page_type,
                    VEDMG_CLASSROOM_PLUGIN_URL . 'admin/css/' . $page_type . '.css',
                    array('vedmg-classroom-admin'),
                    VEDMG_CLASSROOM_VERSION
                );
            }
            
            // Enqueue page-specific JavaScript
            $js_file = VEDMG_CLASSROOM_PLUGIN_DIR . 'admin/js/' . $page_type . '.js';
            if (file_exists($js_file)) {
                wp_enqueue_script(
                    'vedmg-classroom-' . $page_type,
                    VEDMG_CLASSROOM_PLUGIN_URL . 'admin/js/' . $page_type . '.js',
                    array('vedmg-classroom-admin'),
                    VEDMG_CLASSROOM_VERSION,
                    true
                );
            }
        }
        
        // Localize script for AJAX
        wp_localize_script('vedmg-classroom-admin', 'vedmg_classroom_ajax', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('vedmg_classroom_ajax'),
            'api_nonce' => wp_create_nonce('vedmg_classroom_nonce'),
            'current_page' => $page_type,
            'debug_enabled' => get_option('vedmg_classroom_debug_enabled', false),
            'console_logging_enabled' => get_option('vedmg_classroom_console_logging_enabled', false)
        ));
        
        // Log asset loading
        vedmg_log_info('ADMIN', 'Admin assets enqueued for page: ' . $page_type);
    }
    
    /**
     * Get current admin page type
     * Determines which admin page we're currently on
     */
    private static function get_current_admin_page($hook) {
        $page_mapping = array(
            self::$admin_page_slug => 'dashboard',
            self::$admin_page_slug . '-courses' => 'courses',
            self::$admin_page_slug . '-enrollments' => 'enrollments', 
            self::$admin_page_slug . '-sessions' => 'sessions',
            self::$admin_page_slug . '-instructors' => 'instructors',
            self::$admin_page_slug . '-settings' => 'settings'
        );
        
        // Extract page slug from hook
        $page_slug = str_replace('vedmg-classroom_page_', '', $hook);
        $page_slug = str_replace('toplevel_page_', '', $page_slug);
        
        return isset($page_mapping[$page_slug]) ? $page_mapping[$page_slug] : 'dashboard';
    }
    
    /**
     * Handle AJAX actions
     * Processes AJAX requests from admin interface
     */
    public static function handle_ajax_actions() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'vedmg_classroom_ajax')) {
            wp_die('Security check failed');
        }
        
        // Get action
        $action = sanitize_text_field($_POST['action_type']);
        
        // Handle different actions
        switch ($action) {
            case 'test_action':
                self::handle_test_action();
                break;
            // Data retrieval actions
            case 'get_instructor_today_classes':
                self::handle_get_instructor_today_classes();
                break;
            case 'get_instructor_upcoming_classes':
                self::handle_get_instructor_upcoming_classes();
                break;
            case 'get_instructor_all_future_classes':
                self::handle_get_instructor_all_future_classes();
                break;
            case 'get_available_instructors':
                self::handle_get_available_instructors();
                break;
            case 'get_instructor_future_sessions':
                self::handle_get_instructor_future_sessions();
                break;
            case 'get_course_data':
                self::handle_get_course_data();
                break;
            // CRUD operations for courses
            case 'save_course_data':
                self::handle_save_course_data();
                break;
            case 'refresh_courses':
                self::handle_refresh_courses();
                break;
            case 'generate_meeting_link':
                self::handle_generate_meeting_link();
                break;
            // CRUD operations for instructors
            case 'save_instructor_data':
                self::handle_save_instructor_data();
                break;
            case 'refresh_instructors':
                self::handle_refresh_instructors();
                break;
            case 'sync_masterstudy_instructors':
                self::handle_sync_masterstudy_instructors();
                break;
            // CRUD operations for enrollments
            case 'save_enrollment_data':
                self::handle_save_enrollment_data();
                break;
            case 'refresh_enrollments':
                self::handle_refresh_enrollments();
                break;
            // New enrollment enhancement actions
            case 'fetch_classroom_data':
                self::handle_fetch_classroom_data();
                break;
            case 'schedule_lab_session':
                self::handle_schedule_lab_session();
                break;
            // CRUD operations for sessions
            case 'save_session_data':
                self::handle_save_session_data();
                break;
            case 'refresh_sessions':
                self::handle_refresh_sessions();
                break;
            case 'delete_class_session':
                self::handle_delete_class_session();
                break;
            case 'update_session_status':
                self::handle_update_session_status();
                break;
            case 'auto_update_session_statuses':
                self::handle_auto_update_session_statuses();
                break;
            // Dashboard actions
            case 'refresh_dashboard_stats':
                self::handle_refresh_dashboard_stats();
                break;
            // Sync and integration actions
            case 'sync_masterstudy':
                self::handle_sync_masterstudy();
                break;
            case 'sync_woocommerce':
                self::handle_sync_woocommerce();
                break;
            case 'reassign_instructor_classes':
                self::handle_reassign_instructor_classes();
                break;
            case 'get_all_instructors':
                self::handle_get_all_instructors_admin();
                break;
            case 'get_course_creator_and_instructors':
                self::handle_get_course_creator_and_instructors();
                break;
            case 'get_instructor_meetings':
                self::handle_get_instructor_meetings_admin();
                break;
            default:
                wp_send_json_error('Unknown action');
        }
    }
    
    /**
     * Handle test action (placeholder)
     * This is a placeholder for testing AJAX functionality
     */
    private static function handle_test_action() {
        // Log test action
        vedmg_log_admin_action('Test action performed');
        
        // Send success response
        wp_send_json_success('Test action completed successfully');
    }
    
    /**
     * Handle get instructor today classes
     */
    private static function handle_get_instructor_today_classes() {
        // Get instructor ID
        $instructor_id = intval($_POST['instructor_id']);
        
        if (!$instructor_id) {
            wp_send_json_error('Invalid instructor ID');
        }
        
        // Get today's sessions for the instructor
        $sessions = VedMG_ClassRoom_Database_Helper::get_instructor_today_sessions($instructor_id);
        
        // Log action
        vedmg_log_admin_action("Retrieved today's classes for instructor $instructor_id");
        
        // Send response
        wp_send_json_success(array(
            'sessions' => $sessions,
            'count' => count($sessions)
        ));
    }
    
    /**
     * Handle get instructor upcoming classes
     */
    private static function handle_get_instructor_upcoming_classes() {
        // Get instructor ID
        $instructor_id = intval($_POST['instructor_id']);
        
        if (!$instructor_id) {
            wp_send_json_error('Invalid instructor ID');
        }
        
        // Get upcoming sessions for the instructor
        $sessions = VedMG_ClassRoom_Database_Helper::get_instructor_upcoming_sessions($instructor_id);
        
        // Log action
        vedmg_log_admin_action("Retrieved upcoming classes for instructor $instructor_id");
        
        // Send response
        wp_send_json_success(array(
            'sessions' => $sessions,
            'count' => count($sessions)
        ));
    }

    /**
     * Handle get instructor all future classes (including all scheduled recurring sessions)
     */
    private static function handle_get_instructor_all_future_classes() {
        // Get instructor ID
        $instructor_id = intval($_POST['instructor_id']);

        if (!$instructor_id) {
            wp_send_json_error('Invalid instructor ID');
        }

        // Get all future sessions for the instructor (not just next 7 days)
        $sessions = VedMG_ClassRoom_Database_Helper::get_instructor_all_future_sessions($instructor_id);

        // Separate today's and future sessions for better organization
        $today = date('Y-m-d');
        $today_sessions = array();
        $future_sessions = array();

        foreach ($sessions as $session) {
            if ($session->scheduled_date === $today) {
                $today_sessions[] = $session;
            } else {
                $future_sessions[] = $session;
            }
        }

        // Log action
        vedmg_log_admin_action("Retrieved all future classes for instructor $instructor_id");

        // Send response with organized data
        wp_send_json_success(array(
            'all_sessions' => $sessions,
            'today_sessions' => $today_sessions,
            'future_sessions' => $future_sessions,
            'total_count' => count($sessions),
            'today_count' => count($today_sessions),
            'future_count' => count($future_sessions)
        ));
    }

    /**
     * Handle get available instructors
     */
    private static function handle_get_available_instructors() {
        // Get instructor ID to exclude
        $exclude_instructor = intval($_POST['exclude_instructor']);
        
        // Get all instructors
        $all_instructors = VedMG_ClassRoom_Database_Helper::get_instructors();
        
        // Filter out the excluded instructor
        $available_instructors = array();
        foreach ($all_instructors as $instructor) {
            if ($instructor->instructor_id != $exclude_instructor) {
                $available_instructors[] = $instructor;
            }
        }
        
        // Log action
        vedmg_log_admin_action("Retrieved available instructors for reassignment");
        
        // Send response
        wp_send_json_success(array(
            'instructors' => $available_instructors,
            'count' => count($available_instructors)
        ));
    }
    
    /**
     * Handle get instructor future sessions
     */
    private static function handle_get_instructor_future_sessions() {
        // Get instructor ID
        $instructor_id = intval($_POST['instructor_id']);
        
        if (!$instructor_id) {
            wp_send_json_error('Invalid instructor ID');
        }
        
        global $wpdb;
        
        $sessions_table = $wpdb->prefix . 'vedmg_class_sessions';
        $courses_table = $wpdb->prefix . 'vedmg_courses';
        
        try {
            $today = date('Y-m-d');
            
            $sql = "
                SELECT 
                    s.session_id,
                    s.session_title,
                    s.scheduled_date,
                    s.start_time,
                    s.end_time,
                    c.course_name
                FROM $sessions_table s
                LEFT JOIN $courses_table c ON s.course_id = c.course_id
                WHERE c.instructor_id = %d 
                AND s.scheduled_date >= %s
                AND s.session_status != 'cancelled'
                ORDER BY s.scheduled_date ASC, s.start_time ASC
            ";
            
            $sessions = $wpdb->get_results($wpdb->prepare($sql, $instructor_id, $today));
            
            // Log action
            vedmg_log_admin_action("Retrieved future sessions for instructor $instructor_id");
            
            // Send response
            wp_send_json_success(array(
                'sessions' => $sessions,
                'count' => count($sessions)
            ));
            
        } catch (Exception $e) {
            vedmg_log_error('AJAX', 'Error getting future sessions', $e->getMessage());
            wp_send_json_error('Failed to retrieve future sessions');
        }
    }
    
    /**
     * Handle reassign instructor classes
     */
    private static function handle_reassign_instructor_classes() {
        // Get instructor IDs
        $from_instructor_id = intval($_POST['from_instructor_id']);
        $to_instructor_id = intval($_POST['to_instructor_id']);
        
        if (!$from_instructor_id || !$to_instructor_id) {
            wp_send_json_error('Invalid instructor IDs');
        }
        
        if ($from_instructor_id == $to_instructor_id) {
            wp_send_json_error('Cannot reassign to the same instructor');
        }
        
        // Get session IDs if provided (for individual session selection)
        $session_ids = array();
        if (isset($_POST['session_ids']) && is_array($_POST['session_ids'])) {
            $session_ids = array_map('intval', $_POST['session_ids']);
            $session_ids = array_filter($session_ids); // Remove any invalid IDs
        }
        
        // Get meeting IDs if provided (for individual meeting selection)
        $meeting_ids = array();
        if (isset($_POST['meeting_ids']) && is_array($_POST['meeting_ids'])) {
            $meeting_ids = array_map('intval', $_POST['meeting_ids']);
            $meeting_ids = array_filter($meeting_ids); // Remove any invalid IDs
        }
        
        // Perform the reassignment
        $result = VedMG_ClassRoom_Database_Helper::reassign_instructor_sessions($from_instructor_id, $to_instructor_id, $session_ids);
        
        // If there are meetings to reassign as well, handle them
        $meeting_result = array('success' => true, 'affected_meetings' => 0);
        if (!empty($meeting_ids)) {
            $meeting_result = VedMG_ClassRoom_Database_Helper::reassign_instructor_meetings($from_instructor_id, $to_instructor_id, $meeting_ids);
        }
        
        // Log action
        $session_info = empty($session_ids) ? 'all future sessions' : count($session_ids) . ' specific sessions';
        $meeting_info = empty($meeting_ids) ? '' : ', ' . count($meeting_ids) . ' specific meetings';
        vedmg_log_admin_action("Attempted class reassignment from instructor $from_instructor_id to $to_instructor_id ($session_info$meeting_info)");
        
        if ($result['success'] && $meeting_result['success']) {
            // Combine results
            $combined_result = array(
                'success' => true,
                'affected_sessions' => $result['affected_sessions'],
                'affected_meetings' => $meeting_result['affected_meetings'],
                'message' => 'Successfully reassigned classes and meetings'
            );
            wp_send_json_success($combined_result);
        } else {
            // Handle failures
            $error_messages = array();
            if (!$result['success']) {
                $error_messages[] = $result['message'];
            }
            if (!$meeting_result['success']) {
                $error_messages[] = $meeting_result['message'];
            }
            wp_send_json_error(array('message' => implode(', ', $error_messages)));
        }
    }
    
    /**
     * Handle sync MasterStudy action
     */
    private static function handle_sync_masterstudy() {
        // Include MasterStudy integration
        require_once VEDMG_CLASSROOM_PLUGIN_DIR . 'integrations/masterstudy.php';
        
        // Log sync action
        vedmg_log_admin_action('Manual MasterStudy LMS sync initiated');
        
        // Call the manual sync method
        $result = VedMG_ClassRoom_MasterStudy_Integration::manual_sync_all();
        
        // Log sync result
        if ($result['success']) {
            vedmg_log_admin_action('MasterStudy LMS sync completed successfully', $result['message']);
            wp_send_json_success($result);
        } else {
            vedmg_log_admin_action('MasterStudy LMS sync failed', $result['message']);
            wp_send_json_error($result);
        }
    }
    
    /**
     * Handle sync WooCommerce action
     */
    private static function handle_sync_woocommerce() {
        vedmg_log_admin_action('=== ADMIN HANDLER: WooCommerce sync request received ===');
        
        // Include WooCommerce integration
        vedmg_log_admin_action('Loading WooCommerce integration file...');
        require_once VEDMG_CLASSROOM_PLUGIN_DIR . 'integrations/woocommerce.php';
        vedmg_log_admin_action('✓ WooCommerce integration file loaded');
        
        // Log sync action
        vedmg_log_admin_action('Initiating manual WooCommerce sync...');
        
        // Call the manual sync method
        vedmg_log_admin_action('Calling VedMG_ClassRoom_WooCommerce_Integration::manual_sync_all()');
        
        // First run diagnostic
        $diagnostic = VedMG_ClassRoom_WooCommerce_Integration::diagnose_woocommerce_data();
        vedmg_log_admin_action('Diagnostic completed: ' . json_encode($diagnostic));
        
        $result = VedMG_ClassRoom_WooCommerce_Integration::manual_sync_all();
        
        vedmg_log_admin_action('Sync method returned result: ' . json_encode($result));
        
        // Log sync result
        if ($result['success']) {
            vedmg_log_admin_action('✓ WooCommerce sync completed successfully', $result['message']);
            vedmg_log_admin_action('Sending success response to frontend...');
            wp_send_json_success($result);
        } else {
            vedmg_log_admin_action('❌ WooCommerce sync failed', $result['message']);
            vedmg_log_admin_action('Sending error response to frontend...');
            wp_send_json_error($result);
        }
    }
    
    /**
     * Handle get course data
     */
    private static function handle_get_course_data() {
        // Get course ID
        $course_id = intval($_POST['course_id']);
        
        if (!$course_id) {
            wp_send_json_error('Invalid course ID');
        }
        
        // Try to get course data from database using existing method
        $course_data_result = VedMG_ClassRoom_Database_Helper::get_courses(1, 1000); // Get all courses
        $course_data = null;
        
        // Find the specific course
        foreach ($course_data_result['courses'] as $course) {
            if ($course->course_id == $course_id) {
                $course_data = array(
                    'course_id' => $course->course_id,
                    'course_name' => $course->course_name,
                    'course_description' => $course->course_description ?? '',
                    'instructor_name' => $course->instructor_name ?? '',
                    'classroom_status' => $course->classroom_status ?? 'pending'
                );
                break;
            }
        }
        
        // Log action
        vedmg_log_admin_action("Retrieved course data for course $course_id");
        
        // Send response
        if ($course_data) {
            wp_send_json_success($course_data);
        } else {
            // Fallback with placeholder data if not found
            $course_data = array(
                'course_id' => $course_id,
                'course_name' => 'Course ' . $course_id,
                'course_description' => 'Course description',
                'instructor_name' => 'Instructor Name',
                'classroom_status' => 'active'
            );
            wp_send_json_success($course_data);
        }
    }
    
    /**
     * Handle save course data
     */
    private static function handle_save_course_data() {
        // Get form data
        $form_data = $_POST['form_data'];
        $course_id = intval($form_data['course_id']);
        
        if (!$course_id) {
            wp_send_json_error('Invalid course ID');
        }
        
        // Sanitize form data
        $course_data = array(
            'course_name' => sanitize_text_field($form_data['course_name']),
            'course_description' => sanitize_textarea_field($form_data['course_description']),
            'instructor_name' => sanitize_text_field($form_data['instructor_name']),
            'classroom_status' => sanitize_text_field($form_data['classroom_status'])
        );
        
        // Log action
        vedmg_log_admin_action("Course update attempted for course $course_id", json_encode($course_data));
        
        // Update course in database
        $success = VedMG_ClassRoom_Database_Helper::update_course($course_id, $course_data);
        
        if ($success) {
            wp_send_json_success('Course updated successfully');
        } else {
            wp_send_json_error('Failed to update course in database');
        }
    }
    
    /**
     * Handle refresh courses
     */
    private static function handle_refresh_courses() {
        // Log action
        vedmg_log_admin_action('Refreshing courses data');
        
        // Use existing get_courses method
        $course_data = VedMG_ClassRoom_Database_Helper::get_courses();
        $courses = $course_data['courses'];
        
        // Send response
        wp_send_json_success(array(
            'courses' => $courses,
            'count' => count($courses),
            'message' => 'Courses refreshed successfully'
        ));
    }
    
    /**
     * Handle generate meeting link
     */
    private static function handle_generate_meeting_link() {
        // Get meeting data
        $course_id = intval($_POST['course_id']);
        $meeting_title = sanitize_text_field($_POST['meeting_title']);
        $meeting_description = sanitize_textarea_field($_POST['meeting_description']);
        $meeting_date = sanitize_text_field($_POST['meeting_date']);
        $meeting_time = sanitize_text_field($_POST['meeting_time']);
        $meeting_duration = intval($_POST['meeting_duration']);
        $auto_record = isset($_POST['auto_record']) ? 1 : 0;
        
        // Log action
        vedmg_log_admin_action("Generating meeting link for course $course_id");
        
        // Generate meeting link (placeholder implementation)
        $meeting_link = 'https://meet.google.com/generated-link-' . wp_generate_password(10, false);
        
        // Log the generated link
        vedmg_log_admin_action("Generated meeting link: $meeting_link");
        
        // Send response
        wp_send_json_success(array(
            'meeting_link' => $meeting_link,
            'message' => 'Meeting link generated successfully'
        ));
    }
    
    /**
     * Handle save instructor data
     */
    private static function handle_save_instructor_data() {
        // Get form data
        $form_data = $_POST['form_data'];
        $instructor_id = intval($form_data['instructor_id']);
        
        if (!$instructor_id) {
            wp_send_json_error('Invalid instructor ID');
        }
        
        // Sanitize form data
        $instructor_data = array(
            'instructor_name' => sanitize_text_field($form_data['instructor_name']),
            'instructor_email' => sanitize_email($form_data['instructor_email']),
            'instructor_phone' => sanitize_text_field($form_data['instructor_phone']),
            'instructor_bio' => sanitize_textarea_field($form_data['instructor_bio']),
            'status' => sanitize_text_field($form_data['status'])
        );
        
        // Log action
        vedmg_log_admin_action("Instructor update attempted for instructor $instructor_id", json_encode($instructor_data));
        
        // Update instructor in database
        $success = VedMG_ClassRoom_Database_Helper::update_instructor($instructor_id, $instructor_data);
        
        if ($success) {
            wp_send_json_success('Instructor updated successfully');
        } else {
            wp_send_json_error('Failed to update instructor in database');
        }
    }
    
    /**
     * Handle refresh instructors
     */
    private static function handle_refresh_instructors() {
        // Log action
        vedmg_log_admin_action('Refreshing instructors data');
        
        // Use existing get_instructors method
        $instructors = VedMG_ClassRoom_Database_Helper::get_instructors();
        
        // Send response
        wp_send_json_success(array(
            'instructors' => $instructors,
            'count' => count($instructors),
            'message' => 'Instructors refreshed successfully'
        ));
    }
    
    /**
     * Handle sync MasterStudy instructors
     */
    private static function handle_sync_masterstudy_instructors() {
        // Include MasterStudy integration
        if (file_exists(VEDMG_CLASSROOM_PLUGIN_DIR . 'integrations/masterstudy.php')) {
            require_once VEDMG_CLASSROOM_PLUGIN_DIR . 'integrations/masterstudy.php';
            
            // Log action
            vedmg_log_admin_action('Syncing instructors from MasterStudy LMS');
            
            // Check if MasterStudy integration class exists
            if (class_exists('VedMG_ClassRoom_MasterStudy_Integration')) {
                // Attempt to sync instructors
                $result = VedMG_ClassRoom_MasterStudy_Integration::sync_instructors();
                wp_send_json_success($result);
            } else {
                wp_send_json_error('MasterStudy integration not available');
            }
        } else {
            wp_send_json_error('MasterStudy integration file not found');
        }
    }
    
    /**
     * Handle save enrollment data
     */
    private static function handle_save_enrollment_data() {
        // Get form data
        $form_data = $_POST['form_data'];
        $enrollment_id = intval($form_data['enrollment_id']);
        
        if (!$enrollment_id) {
            wp_send_json_error('Invalid enrollment ID');
        }
        
        // Sanitize form data
        $enrollment_data = array(
            'student_name' => sanitize_text_field($form_data['student_name']),
            'student_email' => sanitize_email($form_data['student_email']),
            'course_id' => intval($form_data['course_id']),
            'enrollment_status' => sanitize_text_field($form_data['enrollment_status']),
            'enrollment_date' => sanitize_text_field($form_data['enrollment_date'])
        );
        
        // Log action
        vedmg_log_admin_action("Enrollment update attempted for enrollment $enrollment_id", json_encode($enrollment_data));
        
        // Update enrollment in database
        $success = VedMG_ClassRoom_Database_Helper::update_enrollment($enrollment_id, $enrollment_data);
        
        if ($success) {
            wp_send_json_success('Enrollment updated successfully');
        } else {
            wp_send_json_error('Failed to update enrollment in database');
        }
    }
    
    /**
     * Handle refresh enrollments
     */
    private static function handle_refresh_enrollments() {
        // Log action
        vedmg_log_admin_action('Refreshing enrollments data');
        
        // Use existing get_student_enrollments method
        $enrollment_data = VedMG_ClassRoom_Database_Helper::get_student_enrollments();
        $enrollments = $enrollment_data['enrollments'];
        
        // Send response
        wp_send_json_success(array(
            'enrollments' => $enrollments,
            'count' => count($enrollments),
            'message' => 'Enrollments refreshed successfully'
        ));
    }
    
    /**
     * Handle save session data
     */
    private static function handle_save_session_data() {
        // Get form data
        $form_data = $_POST['form_data'];
        $session_id = intval($form_data['session_id']);
        
        if (!$session_id) {
            wp_send_json_error('Invalid session ID');
        }
        
        // Sanitize form data
        $session_data = array(
            'session_title' => sanitize_text_field($form_data['session_title']),
            'session_description' => sanitize_textarea_field($form_data['session_description']),
            'course_id' => intval($form_data['course_id']),
            'session_date' => sanitize_text_field($form_data['session_date']),
            'session_time' => sanitize_text_field($form_data['session_time']),
            'end_time' => sanitize_text_field($form_data['end_time']),
            'status' => sanitize_text_field($form_data['status'])
        );
        
        // Remove empty values to avoid database issues
        $session_data = array_filter($session_data, function($value) {
            return $value !== '' && $value !== null;
        });
        
        // Log action
        vedmg_log_admin_action("Session update attempted for session $session_id", json_encode($session_data));
        
        // Update session in database
        $success = VedMG_ClassRoom_Database_Helper::update_session($session_id, $session_data);
        
        if ($success) {
            wp_send_json_success('Session updated successfully');
        } else {
            wp_send_json_error('Failed to update session in database');
        }
    }
    
    /**
     * Handle refresh sessions
     */
    private static function handle_refresh_sessions() {
        // Log action
        vedmg_log_admin_action('Refreshing sessions data');
        
        // Use existing get_class_sessions method
        $session_data = VedMG_ClassRoom_Database_Helper::get_class_sessions();
        $sessions = $session_data['sessions'];
        
        // Send response
        wp_send_json_success(array(
            'sessions' => $sessions,
            'count' => count($sessions),
            'message' => 'Sessions refreshed successfully'
        ));
    }
    
    /**
     * Handle delete class session
     */
    private static function handle_delete_class_session() {
        // Verify session ID
        $session_id = intval($_POST['session_id'] ?? 0);

        if (!$session_id) {
            wp_send_json_error('Invalid session ID');
        }

        // Get session details with calendar information
        $session_details = VedMG_ClassRoom_Database_Helper::get_session_with_calendar_info($session_id);

        if (!$session_details) {
            wp_send_json_error('Session not found');
        }

        // Log action
        vedmg_log_admin_action("Session deletion attempted for session $session_id");

        // Delete calendar if calendar_id and instructor_email exist
        if (!empty($session_details->calendar_id) && !empty($session_details->instructor_email)) {
            $calendar_deleted = self::delete_calendar_via_api($session_details->calendar_id, $session_details->instructor_email);

            if (!$calendar_deleted) {
                vedmg_log_error('CALENDAR', "Failed to delete calendar {$session_details->calendar_id} for session {$session_id}");
                wp_send_json_error('Failed to delete calendar. Session deletion cancelled to prevent data inconsistency.');
                return;
            } else {
                vedmg_log_info('CALENDAR', "Successfully deleted calendar {$session_details->calendar_id} for session {$session_id}");
            }
        }

        global $wpdb;
        $sessions_table = $wpdb->prefix . 'vedmg_class_sessions';

        // Delete session from database only after calendar is successfully deleted
        $result = $wpdb->delete(
            $sessions_table,
            array('session_id' => $session_id),
            array('%d')
        );
        
        if ($result !== false) {
            vedmg_log_info('ADMIN', "Session $session_id deleted successfully");
            wp_send_json_success(array(
                'message' => 'Session deleted successfully',
                'session_id' => $session_id
            ));
        } else {
            vedmg_log_error('ADMIN', "Failed to delete session $session_id: " . $wpdb->last_error);
            wp_send_json_error('Failed to delete session from database');
        }
    }

    /**
     * Delete calendar via API
     *
     * @param string $calendar_id Calendar ID to delete
     * @param string $instructor_email Instructor email
     * @return bool True if successful, false otherwise
     */
    private static function delete_calendar_via_api($calendar_id, $instructor_email) {
        vedmg_log_info('CALENDAR', "Attempting to delete calendar: $calendar_id for instructor: $instructor_email");

        // Use the API class to call delete_calendar endpoint
        $api_response = VedMG_ClassRoom_API::call_google_classroom_api('delete_calendar', array(
            'instructor_name' => $instructor_email,
            'calendar_id' => $calendar_id
        ));

        // Check if API response indicates success
        $delete_success = false;
        if (isset($api_response['success']) && $api_response['success']) {
            $delete_success = true;
        } elseif (isset($api_response['message']) && strpos($api_response['message'], 'deleted successfully') !== false) {
            $delete_success = true;
        } elseif (!isset($api_response['error']) && !is_string($api_response)) {
            // If no error and not a string, assume success
            $delete_success = true;
        }

        if ($delete_success) {
            vedmg_log_info('CALENDAR', "Successfully deleted calendar: $calendar_id");

            // Update all database tables that contain this calendar_id
            global $wpdb;
            $courses_table = $wpdb->prefix . 'vedmg_courses';

            // Update courses table to remove calendar_id
            $update_result = $wpdb->update(
                $courses_table,
                array(
                    'calendar_id' => null,
                    'updated_date' => current_time('mysql')
                ),
                array('calendar_id' => $calendar_id),
                array('%s', '%s'),
                array('%s')
            );

            vedmg_log_info('CALENDAR', "Updated $update_result courses to remove calendar_id: $calendar_id");
            return true;
        } else {
            $error_message = 'Failed to delete calendar';
            if (isset($api_response['error'])) {
                $error_message = $api_response['error'];
            } elseif (is_string($api_response)) {
                $error_message = $api_response;
            }

            vedmg_log_error('CALENDAR', "Calendar deletion failed: $error_message", array(
                'calendar_id' => $calendar_id,
                'instructor_email' => $instructor_email,
                'api_response' => $api_response
            ));
            return false;
        }
    }

    /**
     * Handle update session status
     */
    private static function handle_update_session_status() {
        // Verify parameters
        $session_id = intval($_POST['session_id'] ?? 0);
        $status = sanitize_text_field($_POST['status'] ?? '');
        
        if (!$session_id || !$status) {
            wp_send_json_error('Invalid session ID or status');
        }
        
        // Validate status
        $valid_statuses = array('scheduled', 'ongoing', 'completed', 'cancelled');
        if (!in_array($status, $valid_statuses)) {
            wp_send_json_error('Invalid status value');
        }
        
        // Log action
        vedmg_log_admin_action("Session status update attempted for session $session_id to $status");
        
        global $wpdb;
        $sessions_table = $wpdb->prefix . 'vedmg_class_sessions';
        
        // Update session status
        $result = $wpdb->update(
            $sessions_table,
            array('session_status' => $status),
            array('session_id' => $session_id),
            array('%s'),
            array('%d')
        );
        
        if ($result !== false) {
            vedmg_log_info('ADMIN', "Session $session_id status updated to $status");
            wp_send_json_success(array(
                'message' => 'Session status updated successfully',
                'session_id' => $session_id,
                'status' => $status
            ));
        } else {
            vedmg_log_error('ADMIN', "Failed to update session $session_id status: " . $wpdb->last_error);
            wp_send_json_error('Failed to update session status in database');
        }
    }
    
    /**
     * Handle auto-update of session statuses based on current time
     */
    private static function handle_auto_update_session_statuses() {
        try {
            vedmg_log_admin_action('Auto-updating session statuses based on current time');
            
            // Call the database helper function to update statuses
            $updated_count = VedMG_ClassRoom_Database_Helper::auto_update_session_statuses();
            
            wp_send_json_success(array(
                'updated_count' => $updated_count,
                'message' => "Auto-updated $updated_count session statuses",
                'timestamp' => current_time('mysql')
            ));
            
        } catch (Exception $e) {
            vedmg_log_error('ADMIN', 'Error auto-updating session statuses', $e->getMessage());
            wp_send_json_error('Failed to auto-update session statuses: ' . $e->getMessage());
        }
    }
    
    
    /**
     * Handle refresh dashboard stats
     */
    private static function handle_refresh_dashboard_stats() {
        // Log action
        vedmg_log_admin_action('Refreshing dashboard statistics');
        
        // Use existing get_dashboard_stats method
        $stats = VedMG_ClassRoom_Database_Helper::get_dashboard_stats();
        
        // Send response
        wp_send_json_success(array(
            'stats' => $stats,
            'message' => 'Dashboard statistics refreshed successfully'
        ));
    }
    
    /**
     * Handle get all instructors for admin
     */
    private static function handle_get_all_instructors_admin() {
        try {
            // Get all instructors using database helper (same as instructor roster page)
            $instructors_data = VedMG_ClassRoom_Database_Helper::get_instructors_paginated(1, 1000, '', '');
            $instructors = $instructors_data['instructors'];
            
            // Format instructors for dropdown
            $formatted_instructors = array();
            foreach ($instructors as $instructor) {
                $formatted_instructors[] = array(
                    'instructor_id' => $instructor->instructor_id,
                    'instructor_name' => $instructor->instructor_name,
                    'instructor_email' => $instructor->instructor_email,
                    'specialization' => $instructor->specialization ?: 'General',
                    'course_count' => intval($instructor->course_count)
                );
            }
            
            // Log action
            vedmg_log_admin_action('Retrieved ' . count($formatted_instructors) . ' instructors for dropdown');
            
            // Send response
            wp_send_json_success(array(
                'instructors' => $formatted_instructors,
                'message' => 'Instructors retrieved successfully'
            ));
            
        } catch (Exception $e) {
            vedmg_log_error('ADMIN', 'Failed to get instructors', $e->getMessage());
            wp_send_json_error('Failed to get instructors: ' . $e->getMessage());
        }
    }
    
    /**
     * Handle get course creator and instructors
     */
    private static function handle_get_course_creator_and_instructors() {
        $course_id = intval($_POST['course_id']);
        
        if (!$course_id) {
            wp_send_json_error('Invalid course ID');
            return;
        }
        
        try {
            // Get course creator info from course data
            $course_data = VedMG_ClassRoom_Database_Helper::get_course_by_id($course_id);
            $course_creator = null;
            
            if ($course_data) {
                // Get course creator details
                $course_creator = array(
                    'id' => $course_data->instructor_id,
                    'name' => $course_data->instructor_name
                );
                vedmg_log_admin_action('Found course creator: ' . $course_data->instructor_name . ' (ID: ' . $course_data->instructor_id . ') for course ' . $course_id);
            } else {
                vedmg_log_admin_action('No course data found for course ID: ' . $course_id);
            }
            
            // Get all instructors for dropdown
            $instructors_data = VedMG_ClassRoom_Database_Helper::get_instructors_paginated(1, 1000, '', '');
            $instructors = $instructors_data['instructors'];
            
            // Format instructors for dropdown
            $formatted_instructors = array();
            foreach ($instructors as $instructor) {
                $formatted_instructors[] = array(
                    'instructor_id' => $instructor->instructor_id,
                    'instructor_name' => $instructor->instructor_name,
                    'instructor_email' => $instructor->instructor_email,
                    'specialization' => $instructor->specialization ?: 'General',
                    'course_count' => intval($instructor->course_count)
                );
            }
            
            vedmg_log_admin_action('Retrieved course creator and ' . count($formatted_instructors) . ' instructors for course assignment');
            
            // Send response with both course creator and instructors
            wp_send_json_success(array(
                'course_creator' => $course_creator,
                'instructors' => $formatted_instructors,
                'message' => 'Course creator and instructors retrieved successfully'
            ));
            
        } catch (Exception $e) {
            vedmg_log_error('ADMIN', 'Failed to get course creator and instructors', $e->getMessage());
            wp_send_json_error('Failed to get course creator and instructors: ' . $e->getMessage());
        }
    }
    
    /**
     * Handle get instructor meetings for admin
     */
    private static function handle_get_instructor_meetings_admin() {
        $instructor_id = intval($_POST['instructor_id']);

        if (!$instructor_id) {
            wp_send_json_error('Invalid instructor ID');
        }

        try {
            // Use the database helper method
            $meetings = VedMG_ClassRoom_Database_Helper::get_instructor_meetings($instructor_id);

            // Log action
            vedmg_log_admin_action('Retrieved meetings for instructor: ' . $instructor_id);

            // Send response
            wp_send_json_success(array(
                'meetings' => $meetings,
                'instructor_id' => $instructor_id,
                'message' => 'Meetings retrieved successfully'
            ));

        } catch (Exception $e) {
            vedmg_log_error('ADMIN', 'Failed to get instructor meetings', $e->getMessage());
            wp_send_json_error('Failed to get meetings: ' . $e->getMessage());
        }
    }
    
    /**
     * Handle fetch classroom data from Google Classroom API
     * Delegates to the API class for actual processing
     */
    private static function handle_fetch_classroom_data() {
        vedmg_log_admin_action('Fetch classroom data requested');
        
        // Delegate to API class
        VedMG_ClassRoom_API::handle_fetch_classroom_data();
    }
    
    /**
     * Handle schedule lab session
     * Delegates to the API class for actual processing
     */
    private static function handle_schedule_lab_session() {
        vedmg_log_admin_action('Schedule lab session requested');
        
        // Delegate to API class
        VedMG_ClassRoom_API::handle_schedule_lab_session();
    }
    
    /**
     * Handle schedule session AJAX request
     * Processes session scheduling form data and stores in database
     */
    public static function handle_schedule_session_ajax() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'vedmg_schedule_session')) {
            wp_die('Security check failed');
        }
        
        try {
            // Collect and sanitize form data
            $session_data = array(
                'course_id' => intval($_POST['course_id'] ?? 0),
                'session_title' => sanitize_text_field($_POST['session_title'] ?? ''),
                'session_type' => sanitize_text_field($_POST['session_type'] ?? ''),
                'session_date' => sanitize_text_field($_POST['session_date'] ?? ''),
                'session_time' => sanitize_text_field($_POST['session_time'] ?? ''),
                'duration' => intval($_POST['duration'] ?? 60),
                'description' => sanitize_textarea_field($_POST['description'] ?? ''),
                'is_recurring' => isset($_POST['is_recurring']) ? 1 : 0,
                'recurring_pattern' => sanitize_text_field($_POST['recurring_pattern'] ?? ''),
                'recurring_count' => intval($_POST['recurring_count'] ?? 1),
                'recurring_end_date' => sanitize_text_field($_POST['recurring_end_date'] ?? ''),
                'selected_student_ids' => json_decode(stripslashes($_POST['selected_student_ids'] ?? '[]'), true)
            );
            
            // Collect recurring days/dates based on pattern
            if (isset($_POST['recurring_days'])) {
                $session_data['recurring_days'] = array_map('sanitize_text_field', $_POST['recurring_days']);
            }
            
            if (isset($_POST['recurring_dates'])) {
                $session_data['recurring_dates'] = array_map('intval', $_POST['recurring_dates']);
            }
            
            // Validate required fields
            if (empty($session_data['session_title']) || empty($session_data['session_type']) || 
                empty($session_data['session_date']) || empty($session_data['session_time'])) {
                wp_send_json_error('Please fill in all required fields.');
                return;
            }
            
            // Enhanced validation for recurring sessions
            if ($session_data['is_recurring']) {
                // Validate number of sessions
                if (empty($session_data['recurring_count']) || $session_data['recurring_count'] < 1) {
                    wp_send_json_error('Please enter a valid number of sessions (minimum 1).');
                    return;
                }
                
                if ($session_data['recurring_count'] > 100) {
                    wp_send_json_error('Number of sessions cannot exceed 100 for safety reasons.');
                    return;
                }
                
                // Validate end date
                if (empty($session_data['recurring_end_date'])) {
                    wp_send_json_error('Please select an end date for recurring sessions.');
                    return;
                }
                
                // Validate end date is in the future
                $end_date = new DateTime($session_data['recurring_end_date']);
                $today = new DateTime();
                if ($end_date <= $today) {
                    wp_send_json_error('End date must be in the future.');
                    return;
                }
                
                // Validate pattern-specific requirements
                if (in_array($session_data['recurring_pattern'], ['weekly', 'bi-weekly'])) {
                    if (empty($session_data['recurring_days']) || count($session_data['recurring_days']) === 0) {
                        wp_send_json_error('Please select at least one day of the week for ' . $session_data['recurring_pattern'] . ' sessions.');
                        return;
                    }
                } elseif ($session_data['recurring_pattern'] === 'monthly') {
                    if (empty($session_data['recurring_dates']) || count($session_data['recurring_dates']) === 0) {
                        wp_send_json_error('Please select at least one date of the month for monthly sessions.');
                        return;
                    }
                }
            }
            
            // Store session in database
            $session_id = VedMG_ClassRoom_Database_Helper::store_scheduled_session($session_data);
            
            if (!$session_id) {
                wp_send_json_error('Failed to store session in database.');
                return;
            }
            
            // Log session creation
            vedmg_log_admin_action('Session scheduled successfully', array(
                'session_id' => $session_id,
                'session_type' => $session_data['session_type'],
                'student_count' => count($session_data['selected_student_ids'])
            ));
            
            // TODO: Make API call to create Google Meet
            // This will be implemented when the API is available
            // For now, we'll just store the session data
            
            // Determine affected students based on session type
            $affected_students = array();
            if ($session_data['session_type'] === 'group') {
                $affected_students = $session_data['selected_student_ids'];
            } else {
                // For class-wide sessions, get all students in the course
                // This is a placeholder - actual implementation would fetch from enrollments
                $affected_students = array(); // Will be populated later
            }
            
            // Send success response
            wp_send_json_success(array(
                'session_id' => $session_id,
                'message' => 'Session scheduled successfully!',
                'affected_students' => $affected_students
            ));
            
        } catch (Exception $e) {
            vedmg_log_error('ADMIN', 'Error scheduling session', $e->getMessage());
            wp_send_json_error('Error scheduling session: ' . $e->getMessage());
        }
    }

    /**
     * Handle get schedule course data AJAX request
     * Fetches course data needed for schedule lab modal
     */
    public static function handle_get_schedule_course_data_ajax() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'vedmg_classroom_nonce')) {
            wp_send_json_error('Security check failed');
            return;
        }

        // Check user permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
            return;
        }

        $course_id = intval($_POST['course_id']);
        $student_id = intval($_POST['student_id']);

        if (empty($course_id)) {
            wp_send_json_error('Missing course ID');
            return;
        }

        try {
            global $wpdb;

            // Get course data
            $courses_table = $wpdb->prefix . 'vedmg_courses';
            $course = $wpdb->get_row($wpdb->prepare(
                "SELECT course_id, course_name, calendar_id, meeting_link, instructor_email, instructor_name
                 FROM $courses_table WHERE course_id = %d",
                $course_id
            ));

            if (!$course) {
                wp_send_json_error('Course not found');
                return;
            }

            // Get student data if provided
            $student_data = null;
            if ($student_id) {
                $user = get_user_by('ID', $student_id);
                if ($user) {
                    $student_data = [
                        'id' => $user->ID,
                        'name' => $user->display_name,
                        'email' => $user->user_email
                    ];
                }
            }

            wp_send_json_success([
                'course' => [
                    'course_id' => $course->course_id,
                    'course_name' => $course->course_name,
                    'calendar_id' => $course->calendar_id,
                    'meeting_link' => $course->meeting_link,
                    'instructor_email' => $course->instructor_email,
                    'instructor_name' => $course->instructor_name
                ],
                'student' => $student_data
            ]);

        } catch (Exception $e) {
            vedmg_log_error('ADMIN', 'Error fetching schedule course data', $e->getMessage());
            wp_send_json_error('Error fetching course data: ' . $e->getMessage());
        }
    }
    
    /**
     * Handle get session details AJAX request
     */
    public static function handle_get_session_details_ajax() {
        // Verify nonce for security
        if (!wp_verify_nonce($_POST['nonce'], 'vedmg_get_session_details')) {
            wp_send_json_error('Security check failed');
            return;
        }
        
        // Check user capabilities
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
            return;
        }
        
        try {
            $student_id = intval($_POST['student_id']);
            
            if (!$student_id) {
                wp_send_json_error('Invalid student ID');
                return;
            }
            
            // Get session details for the student
            $session_details = VedMG_ClassRoom_Database_Helper::get_student_session_details($student_id);
            
            if (!$session_details) {
                wp_send_json_error('No sessions found for this student');
                return;
            }
            
            // Format the session details for display
            $formatted_sessions = array();
            foreach ($session_details as $session) {
                $formatted_session = array(
                    'session_id' => $session->session_id,
                    'session_title' => $session->session_title,
                    'session_type' => ucfirst($session->session_type),
                    'session_date' => date('F j, Y', strtotime($session->scheduled_date)),
                    'session_time' => date('g:i A', strtotime($session->start_time)),
                    'duration' => $session->duration_minutes . ' minutes',
                    'instructor' => $session->assigned_instructor_id ? 'Instructor ID: ' . $session->assigned_instructor_id : 'Not assigned',
                    'description' => $session->session_description ?: 'No description provided',
                    'total_enrolled' => $session->total_enrolled,
                    'enrolled_students' => $session->enrolled_students,
                    'is_recurring' => $session->is_recurring ? 'Yes' : 'No',
                    'recurring_pattern' => $session->recurring_pattern ?: 'N/A',
                    'recurring_count' => $session->recurring_count ?: 'N/A',
                    'recurring_end_date' => $session->recurring_end_date ? date('F j, Y', strtotime($session->recurring_end_date)) : 'N/A',
                    'student_name' => $session->student_name,
                    'student_email' => $session->student_email,
                    'course_name' => $session->course_name
                );
                $formatted_sessions[] = $formatted_session;
            }
            
            // Send success response
            wp_send_json_success(array(
                'sessions' => $formatted_sessions,
                'student_id' => $student_id
            ));
            
        } catch (Exception $e) {
            vedmg_log_error('ADMIN', 'Error fetching session details', $e->getMessage());
            wp_send_json_error('Error fetching session details: ' . $e->getMessage());
        }
    }
    
    /**
     * Handle sync instructors AJAX request
     */
    public static function handle_sync_instructors_ajax() {
        // Verify nonce for security
        if (!wp_verify_nonce($_POST['nonce'], 'vedmg_classroom_nonce')) {
            wp_send_json_error('Security check failed');
            return;
        }
        
        // Check user permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
            return;
        }
        
        vedmg_log_admin_action('Started instructor sync from WordPress');
        
        try {
            global $wpdb;
            $sync_table = $wpdb->prefix . 'vedmg_instructor_sync';

            // Step 1: Mark all existing records as 'pending_verification' to track which ones are still valid
            $wpdb->update(
                $sync_table,
                array('sync_status' => 'pending_verification'),
                array('sync_status' => 'synced'),
                array('%s'),
                array('%s')
            );

            // Step 2: Get all users with instructor role
            $instructor_users = get_users(array(
                'role__in' => array('stm_lms_instructor', 'instructor', 'administrator'),
                'fields' => array('ID', 'display_name', 'user_email', 'user_login'),
                'meta_query' => array(
                    'relation' => 'OR',
                    array(
                        'key' => 'wp_capabilities',
                        'value' => 'stm_lms_instructor',
                        'compare' => 'LIKE'
                    ),
                    array(
                        'key' => 'wp_capabilities',
                        'value' => 'instructor',
                        'compare' => 'LIKE'
                    )
                )
            ));

            $synced_count = 0;
            $updated_count = 0;
            $deleted_count = 0;
            $errors = array();
            
            foreach ($instructor_users as $user) {
                try {
                    // Get additional user meta
                    $phone = get_user_meta($user->ID, 'phone', true) ?: get_user_meta($user->ID, 'billing_phone', true);
                    
                    // Check if instructor already exists
                    $existing = $wpdb->get_row($wpdb->prepare(
                        "SELECT * FROM $sync_table WHERE wordpress_user_id = %d",
                        $user->ID
                    ));
                    
                    if ($existing) {
                        // Update existing record
                        $result = $wpdb->update(
                            $sync_table,
                            array(
                                'instructor_name' => $user->display_name,
                                'instructor_email' => $user->user_email,
                                'instructor_phone' => $phone,
                                'sync_status' => 'synced',
                                'last_synced_date' => current_time('mysql'),
                                'updated_date' => current_time('mysql')
                            ),
                            array('wordpress_user_id' => $user->ID),
                            array('%s', '%s', '%s', '%s', '%s', '%s'),
                            array('%d')
                        );
                        
                        if ($result !== false) {
                            $updated_count++;
                            vedmg_log_info('INSTRUCTOR_SYNC', "Updated instructor: {$user->display_name} (ID: {$user->ID})");
                        }
                    } else {
                        // Insert new record
                        $result = $wpdb->insert(
                            $sync_table,
                            array(
                                'wordpress_user_id' => $user->ID,
                                'instructor_name' => $user->display_name,
                                'instructor_email' => $user->user_email,
                                'instructor_phone' => $phone,
                                'sync_status' => 'synced',
                                'last_synced_date' => current_time('mysql'),
                                'created_date' => current_time('mysql')
                            ),
                            array('%d', '%s', '%s', '%s', '%s', '%s', '%s')
                        );
                        
                        if ($result) {
                            $synced_count++;
                            vedmg_log_info('INSTRUCTOR_SYNC', "Synced new instructor: {$user->display_name} (ID: {$user->ID})");
                        }
                    }
                    
                } catch (Exception $e) {
                    $errors[] = "Error syncing {$user->display_name}: " . $e->getMessage();
                    vedmg_log_error('INSTRUCTOR_SYNC', "Failed to sync instructor {$user->display_name}", $e->getMessage());
                }
            }

            // Step 3: Handle deleted users and duplicates - remove instructors that no longer exist in WordPress
            $deleted_count = 0;
            $existing_user_ids = array_map(function($user) { return $user->ID; }, $instructor_users);

            // Get all synced instructors from database
            $synced_instructors = $wpdb->get_results(
                "SELECT wordpress_user_id, instructor_name, instructor_email FROM $sync_table WHERE sync_status = 'synced'"
            );

            vedmg_log_info('INSTRUCTOR_SYNC', 'Found ' . count($synced_instructors) . ' synced instructors in database');
            vedmg_log_info('INSTRUCTOR_SYNC', 'Found ' . count($existing_user_ids) . ' current WordPress instructor users');

            foreach ($synced_instructors as $synced_instructor) {
                // If this instructor's WordPress user no longer exists, remove from sync table
                if (!in_array($synced_instructor->wordpress_user_id, $existing_user_ids)) {
                    $delete_result = $wpdb->delete(
                        $sync_table,
                        array('wordpress_user_id' => $synced_instructor->wordpress_user_id),
                        array('%d')
                    );

                    if ($delete_result !== false) {
                        $deleted_count++;
                        vedmg_log_info('INSTRUCTOR_SYNC', "Removed deleted instructor: {$synced_instructor->instructor_name} (ID: {$synced_instructor->wordpress_user_id})");
                    } else {
                        vedmg_log_error('INSTRUCTOR_SYNC', "Failed to delete instructor: {$synced_instructor->instructor_name} (ID: {$synced_instructor->wordpress_user_id})");
                    }
                }
            }

            // Step 4: Remove duplicate entries (same email but different user IDs)
            $duplicate_count = 0;
            $email_user_map = array();

            // Build map of current emails to user IDs
            foreach ($instructor_users as $user) {
                $email_user_map[$user->user_email] = $user->ID;
            }

            // Find and remove duplicates
            $all_sync_records = $wpdb->get_results(
                "SELECT * FROM $sync_table WHERE sync_status = 'synced' ORDER BY last_synced_date DESC"
            );

            $seen_emails = array();
            foreach ($all_sync_records as $record) {
                if (isset($seen_emails[$record->instructor_email])) {
                    // This is a duplicate - remove it
                    $delete_result = $wpdb->delete(
                        $sync_table,
                        array('wordpress_user_id' => $record->wordpress_user_id),
                        array('%d')
                    );

                    if ($delete_result !== false) {
                        $duplicate_count++;
                        vedmg_log_info('INSTRUCTOR_SYNC', "Removed duplicate instructor: {$record->instructor_name} (ID: {$record->wordpress_user_id})");
                    }
                } else {
                    $seen_emails[$record->instructor_email] = $record->wordpress_user_id;
                }
            }

            $total_removed = $deleted_count + $duplicate_count;
            $message = "Sync completed! {$synced_count} new instructors added, {$updated_count} updated";
            if ($total_removed > 0) {
                $message .= ", {$total_removed} removed ({$deleted_count} deleted users, {$duplicate_count} duplicates)";
            }
            $message .= ".";

            $response_data = array(
                'message' => $message,
                'synced_count' => $synced_count,
                'updated_count' => $updated_count,
                'deleted_count' => $deleted_count,
                'duplicate_count' => $duplicate_count,
                'total_removed' => $total_removed,
                'total_count' => count($instructor_users),
                'errors' => $errors
            );
            
            vedmg_log_admin_action('Instructor sync completed', "Synced: {$synced_count}, Updated: {$updated_count}");
            
            wp_send_json_success($response_data);
            
        } catch (Exception $e) {
            vedmg_log_error('INSTRUCTOR_SYNC', 'Instructor sync failed', $e->getMessage());
            wp_send_json_error('Sync failed: ' . $e->getMessage());
        }
    }
    
    /**
     * Handle get all instructors AJAX request
     */
    public static function handle_get_all_instructors_ajax() {
        // Verify nonce for security
        if (!wp_verify_nonce($_POST['nonce'], 'vedmg_classroom_nonce')) {
            wp_send_json_error('Security check failed');
            return;
        }
        
        // Check user permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
            return;
        }
        
        vedmg_log_info('INSTRUCTOR_SYNC', 'Getting all instructors for dropdown');
        
        try {
            // Get instructors using improved method that prioritizes instructor_sync table
            $instructors = self::get_instructors_for_dropdown();

            // Format instructors for dropdown
            $formatted_instructors = array();
            foreach ($instructors as $instructor) {
                $formatted_instructors[] = array(
                    'instructor_id' => $instructor->instructor_id,
                    'instructor_name' => $instructor->instructor_name,
                    'instructor_email' => $instructor->instructor_email,
                    'instructor_phone' => $instructor->instructor_phone
                );
            }
            
            vedmg_log_info('INSTRUCTOR_SYNC', 'Found ' . count($formatted_instructors) . ' instructors');
            
            wp_send_json_success(array(
                'message' => 'Instructors retrieved successfully',
                'instructors' => $formatted_instructors
            ));
            
        } catch (Exception $e) {
            vedmg_log_error('INSTRUCTOR_SYNC', 'Failed to get instructors', $e->getMessage());
            wp_send_json_error('Failed to get instructors: ' . $e->getMessage());
        }
    }

    /**
     * Get instructors for dropdown with improved name handling
     * Prioritizes instructor_sync table for updated names, falls back to courses table
     *
     * @return array Array of instructor objects
     */
    private static function get_instructors_for_dropdown() {
        global $wpdb;

        $sync_table = $wpdb->prefix . 'vedmg_instructor_sync';
        $courses_table = $wpdb->prefix . 'vedmg_courses';

        // First, get instructors from instructor_sync table (these have updated names)
        $sync_instructors = $wpdb->get_results($wpdb->prepare("
            SELECT
                s.wordpress_user_id as instructor_id,
                s.instructor_name,
                s.instructor_email,
                s.instructor_phone,
                COALESCE(course_counts.course_count, 0) as course_count,
                'synced' as source
            FROM $sync_table s
            LEFT JOIN (
                SELECT instructor_id, COUNT(*) as course_count
                FROM $courses_table
                WHERE instructor_id > 0
                GROUP BY instructor_id
            ) course_counts ON s.wordpress_user_id = course_counts.instructor_id
            WHERE s.sync_status = 'synced'
            ORDER BY s.instructor_name ASC
        "));

        // Get IDs of instructors already found in sync table
        $synced_instructor_ids = array();
        foreach ($sync_instructors as $instructor) {
            $synced_instructor_ids[] = $instructor->instructor_id;
        }

        // Then, get instructors from courses table who are NOT in sync table
        $courses_instructors = array();
        if (!empty($synced_instructor_ids)) {
            $placeholders = implode(',', array_fill(0, count($synced_instructor_ids), '%d'));
            $courses_instructors = $wpdb->get_results($wpdb->prepare("
                SELECT DISTINCT
                    c.instructor_id,
                    c.instructor_name,
                    c.instructor_email,
                    c.instructor_phone,
                    COUNT(c.course_id) as course_count,
                    'courses' as source
                FROM $courses_table c
                WHERE c.instructor_id > 0
                AND c.instructor_id NOT IN ($placeholders)
                GROUP BY c.instructor_id, c.instructor_name, c.instructor_email
                ORDER BY c.instructor_name ASC
            ", $synced_instructor_ids));
        } else {
            // If no synced instructors, get all from courses table
            $courses_instructors = $wpdb->get_results("
                SELECT DISTINCT
                    c.instructor_id,
                    c.instructor_name,
                    c.instructor_email,
                    c.instructor_phone,
                    COUNT(c.course_id) as course_count,
                    'courses' as source
                FROM $courses_table c
                WHERE c.instructor_id > 0
                GROUP BY c.instructor_id, c.instructor_name, c.instructor_email
                ORDER BY c.instructor_name ASC
            ");
        }

        // Combine results (sync table instructors first, then courses table instructors)
        $all_instructors = array_merge($sync_instructors, $courses_instructors);

        vedmg_log_info('INSTRUCTOR_SYNC', 'Found ' . count($sync_instructors) . ' instructors from sync table, ' . count($courses_instructors) . ' from courses table');

        return $all_instructors;
    }

    /**
     * Handle Google Classroom sync AJAX request
     */
    public static function handle_sync_google_classroom_ajax() {
        // Verify nonce for security
        if (!wp_verify_nonce($_POST['nonce'], 'vedmg_classroom_nonce')) {
            wp_send_json_error('Security check failed');
            return;
        }
        
        // Check user permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
            return;
        }
        
        $instructor_email = sanitize_email($_POST['instructor_email']);
        $instructor_name = sanitize_text_field($_POST['instructor_name'] ?? '');
        $instructor_id = intval($_POST['instructor_id'] ?? 0);
        
        if (empty($instructor_email)) {
            wp_send_json_error('Instructor email is required');
            return;
        }
        
        if (empty($instructor_name) || $instructor_id <= 0) {
            wp_send_json_error('Valid instructor selection is required');
            return;
        }
        
        vedmg_log_info('GOOGLE_SYNC', 'Starting Google Classroom sync for: ' . $instructor_email . ' (Selected: ' . $instructor_name . ', ID: ' . $instructor_id . ')');
        
        try {
            // Update instructor sync status to syncing
            global $wpdb;
            $sync_table = $wpdb->prefix . 'vedmg_instructor_sync';
            
            $wpdb->update(
                $sync_table,
                array('google_sync_status' => 'syncing'),
                array('instructor_email' => $instructor_email),
                array('%s'),
                array('%s')
            );
            
            // Step 1: Get ALL courses from the courses page/database (not just instructor's courses)
            $all_courses = self::get_all_courses_for_matching();

            // Step 1.5: Check if there are any existing LMS courses to match against
            if (empty($all_courses)) {
                throw new Exception('No existing LMS courses found in the system. Please sync with MasterStudy LMS first to create courses before syncing with Google Classroom.');
            }

            // Step 2: Fetch courses from Google Classroom API using provided email
            $google_courses = self::fetch_google_classroom_courses($instructor_email);

            if ($google_courses === false) {
                throw new Exception('Failed to fetch courses from Google Classroom API. Please check the email address or API connectivity.');
            }
            
            if (empty($google_courses)) {
                // Provide detailed explanation for no courses found
                $error_message = "No courses found in Google Classroom for '{$instructor_email}'.\n\n";
                $error_message .= "POSSIBLE REASONS:\n";
                $error_message .= "• The email address is not connected to any Google Classroom courses\n";
                $error_message .= "• The instructor hasn't created any courses in Google Classroom yet\n";
                $error_message .= "• The Google account may not have teaching permissions\n";
                $error_message .= "• The API key might not have access to this instructor's courses\n\n";
                $error_message .= "WHAT TO DO:\n";
                $error_message .= "• Verify the email address is correct\n";
                $error_message .= "• Ask the instructor to check their Google Classroom account\n";
                $error_message .= "• Ensure the instructor has created at least one course in Google Classroom";
                
                throw new Exception($error_message);
            }
            
            // Step 3: Match Google Classroom courses with ALL courses from courses page using single-word matching
            $match_result = self::enhanced_match_and_update_courses($all_courses, $google_courses, $instructor_id, $instructor_name, $instructor_email);

            // Step 4: Run fallback calendar ID sync for courses that don't have calendar IDs from Google Classroom API
            $fallback_result = self::fallback_sync_calendar_ids($instructor_email);
            vedmg_log_info('GOOGLE_SYNC', 'Fallback calendar sync completed', $fallback_result);

            // Count how many Google Classroom courses were found (this is the classroom count)
            $google_classroom_count = count($google_courses);
            
            // Update instructor sync status to success
            $update_result = $wpdb->update(
                $sync_table,
                array(
                    'google_sync_status' => 'synced',
                    'last_google_sync_date' => current_time('mysql'),
                    'google_classroom_count' => intval($google_classroom_count),
                    'sync_error_message' => null
                ),
                array('instructor_email' => $instructor_email),
                array('%s', '%s', '%d', '%s'),
                array('%s')
            );
            
            // Log the update result for debugging
            if ($update_result === false) {
                vedmg_log_error('GOOGLE_SYNC', 'Failed to update instructor sync status', $wpdb->last_error);
                throw new Exception('Failed to update instructor sync status: ' . $wpdb->last_error);
            } elseif ($update_result === 0) {
                // If no rows were affected, try updating by WordPress user ID as fallback
                vedmg_log_warning('GOOGLE_SYNC', "Email-based update affected 0 rows. Trying WordPress user ID: {$instructor_id}");
                
                $fallback_result = $wpdb->update(
                    $sync_table,
                    array(
                        'google_sync_status' => 'synced',
                        'last_google_sync_date' => current_time('mysql'),
                        'google_classroom_count' => intval($google_classroom_count),
                        'sync_error_message' => null
                    ),
                    array('wordpress_user_id' => $instructor_id),
                    array('%s', '%s', '%d', '%s'),
                    array('%d')
                );
                
                if ($fallback_result === false) {
                    vedmg_log_error('GOOGLE_SYNC', 'Fallback update also failed', $wpdb->last_error);
                    throw new Exception('Failed to update instructor sync status (both email and ID): ' . $wpdb->last_error);
                } else {
                    vedmg_log_info('GOOGLE_SYNC', "Fallback update successful. Rows affected: {$fallback_result}");
                    $update_result = $fallback_result; // Use fallback result for verification
                }
            } else {
                vedmg_log_info('GOOGLE_SYNC', "Successfully updated instructor sync status. Rows affected: {$update_result}");
            }
            
            // Verify the update was successful by reading back the data
            $verification_query = $wpdb->prepare(
                "SELECT google_sync_status, google_classroom_count, instructor_email, wordpress_user_id FROM $sync_table WHERE instructor_email = %s OR wordpress_user_id = %d",
                $instructor_email, $instructor_id
            );
            $verification_result = $wpdb->get_row($verification_query);
            
            if ($verification_result) {
                vedmg_log_info('GOOGLE_SYNC', "Verification successful - Status: {$verification_result->google_sync_status}, Count: {$verification_result->google_classroom_count}, Email: {$verification_result->instructor_email}, WP_ID: {$verification_result->wordpress_user_id}");
            } else {
                vedmg_log_error('GOOGLE_SYNC', "Could not verify instructor sync update for email: {$instructor_email} or WordPress ID: {$instructor_id}");
                throw new Exception('Instructor sync update verification failed');
            }
            
            vedmg_log_info('GOOGLE_SYNC', 'Google Classroom sync completed', json_encode($match_result));
            
            // Prepare detailed feedback message
            $feedback_message = "Google Classroom sync completed successfully!\n\n";
            $feedback_message .= "📊 SYNC SUMMARY:\n";
            $feedback_message .= "• Instructor: {$instructor_name} ({$instructor_email})\n";
            $feedback_message .= "• Database courses found: " . count($all_courses) . "\n";
            $feedback_message .= "• Google Classroom courses: " . count($google_courses) . "\n";
            $feedback_message .= "• Matches found: {$match_result['matches']}\n";
            $feedback_message .= "• Courses updated: {$match_result['updated']}\n";
            $feedback_message .= "• New courses created: {$match_result['created']}\n";
            
            if ($match_result['matches'] > 0) {
                $feedback_message .= "\n✅ SUCCESSFUL MATCHES:\n";
                foreach ($match_result['details'] as $detail) {
                    if ($detail['action'] === 'updated') {
                        $feedback_message .= "• '{$detail['db_course']}' ↔ '{$detail['google_course']}' (matched on: '{$detail['matched_word']}')\n";
                    }
                }
            }
            
            if ($match_result['created'] > 0) {
                $feedback_message .= "\n🆕 NEW COURSES CREATED:\n";
                foreach ($match_result['details'] as $detail) {
                    if ($detail['action'] === 'created') {
                        $feedback_message .= "• '{$detail['google_course']}' (attributed to {$instructor_name})\n";
                    }
                }
            }
            
            // Show detailed Google Classroom findings
            if (isset($match_result['google_classroom_details']) && !empty($match_result['google_classroom_details'])) {
                $feedback_message .= "\n📱 GOOGLE CLASSROOM COURSES FOUND:\n";
                foreach ($match_result['google_classroom_details'] as $classroom) {
                    $status_icon = $classroom['matched'] ? '✅' : '❌';
                    $feedback_message .= "{$status_icon} '{$classroom['name']}' (ID: {$classroom['id']})\n";
                    if ($classroom['matched']) {
                        $feedback_message .= "   → {$classroom['match_reason']}\n";
                    } else {
                        $feedback_message .= "   → No matching course found in courses page\n";
                    }
                }
            }
            
            // Check for unmatched cases and provide explanations
            $unmatched_db_courses = count($all_courses) - $match_result['matches'];
            $unmatched_google_courses = count($google_courses) - $match_result['matches'] - $match_result['created'];
            
            if ($unmatched_db_courses > 0 || $unmatched_google_courses > 0) {
                $feedback_message .= "\n⚠️ POSSIBLE REASONS FOR UNMATCHED COURSES:\n";
                
                if ($unmatched_db_courses > 0) {
                    $feedback_message .= "• {$unmatched_db_courses} database course(s) had no matching Google Classroom names\n";
                    $feedback_message .= "  → Check if course names share at least one meaningful word\n";
                }
                
                if ($unmatched_google_courses > 0) {
                    $feedback_message .= "• {$unmatched_google_courses} Google Classroom course(s) already exist in database\n";
                    $feedback_message .= "  → These courses may be attributed to other instructors\n";
                }
                
                $feedback_message .= "\n💡 MATCHING CRITERIA:\n";
                $feedback_message .= "• Courses must share at least one meaningful word (length > 2)\n";
                $feedback_message .= "• Common words like 'the', 'of', 'course' are ignored\n";
                $feedback_message .= "• Matching is case-insensitive and supports partial matches\n";
            }
            
            wp_send_json_success(array(
                'message' => $feedback_message,
                'instructor_email' => $instructor_email,
                'instructor_name' => $instructor_name,
                'database_courses' => count($all_courses),
                'google_courses' => count($google_courses),
                'matches_found' => $match_result['matches'],
                'courses_updated' => $match_result['updated'],
                'created' => $match_result['created'],
                'active_courses_count' => intval($google_classroom_count),
                'matched_details' => $match_result['details'],
                'unmatched_db_courses' => $unmatched_db_courses,
                'unmatched_google_courses' => $unmatched_google_courses,
                'database_updated' => ($verification_result && $verification_result->google_sync_status === 'synced')
            ));
            
        } catch (Exception $e) {
            // Update sync status to failed
            global $wpdb;
            $sync_table = $wpdb->prefix . 'vedmg_instructor_sync';
            $wpdb->update(
                $sync_table,
                array(
                    'google_sync_status' => 'failed',
                    'sync_error_message' => $e->getMessage()
                ),
                array('instructor_email' => $instructor_email),
                array('%s', '%s'),
                array('%s')
            );
            
            vedmg_log_error('GOOGLE_SYNC', 'Google Classroom sync failed', $e->getMessage());
            wp_send_json_error('Sync failed: ' . $e->getMessage());
        }
    }
    
    /**
     * Get ALL courses from database for matching with Google Classroom
     * This is used to match Google Classroom courses with ANY course in the system,
     * not just the instructor's own courses
     * 
     * @return array Array of all courses
     */
    private static function get_all_courses_for_matching() {
        global $wpdb;
        
        $courses_table = $wpdb->prefix . 'vedmg_courses';
        
        $query = "SELECT course_id, course_name, instructor_id, instructor_name, instructor_email, classroom_status 
                  FROM $courses_table 
                  WHERE course_status != 'deleted'
                  ORDER BY course_name ASC";
        
        $courses = $wpdb->get_results($query);
        
        vedmg_log_info('GOOGLE_SYNC', 'Loaded ' . count($courses) . ' courses for matching from courses page');
        
        return $courses ?: array();
    }

    /**
     * Get instructor's courses from database by instructor ID
     * 
     * @param int $instructor_id Instructor ID
     * @return array Array of instructor's courses
     */
    private static function get_instructor_courses_by_id($instructor_id) {
        global $wpdb;
        
        $courses_table = $wpdb->prefix . 'vedmg_courses';
        
        // Get all courses for this instructor ID, prioritizing pending status
        $instructor_courses = $wpdb->get_results($wpdb->prepare(
            "SELECT course_id, course_name, instructor_email, classroom_status, 
                    google_classroom_id, instructor_name, instructor_id
             FROM $courses_table 
             WHERE instructor_id = %d
             ORDER BY 
                CASE classroom_status 
                    WHEN 'pending' THEN 1 
                    WHEN 'created' THEN 2 
                    WHEN 'active' THEN 3 
                    ELSE 4 
                END",
            $instructor_id
        ));
        
        vedmg_log_info('GOOGLE_SYNC', 'Found ' . count($instructor_courses) . ' courses for instructor ID: ' . $instructor_id);
        
        return $instructor_courses ?: array();
    }
    
    /**
     * Fetch courses from Google Classroom API for specific instructor
     *
     * @param string $instructor_email Instructor email address
     * @return array|false Array of courses or false on failure
     */
    private static function fetch_google_classroom_courses($instructor_email) {
        $api_url = 'https://gclassroom-839391304260.us-central1.run.app/list_courses';
        $api_key = 'G$$gle@VedMG!@#';

        vedmg_log_info('GOOGLE_SYNC', 'Fetching courses from: ' . $api_url . ' for: ' . $instructor_email);

        $post_data = json_encode([
            'instructor_email' => $instructor_email
        ]);

        $response = wp_remote_post($api_url, array(
            'timeout' => 30,
            'headers' => array(
                'Content-Type' => 'application/json',
                'x-api-key' => $api_key,
                'Content-Length' => strlen($post_data)
            ),
            'body' => $post_data
        ));

        if (is_wp_error($response)) {
            vedmg_log_error('GOOGLE_SYNC', 'API request failed', $response->get_error_message());
            return false;
        }

        $response_code = wp_remote_retrieve_response_code($response);
        $response_body = wp_remote_retrieve_body($response);

        vedmg_log_info('GOOGLE_SYNC', 'API Response', array(
            'status_code' => $response_code,
            'body' => $response_body
        ));

        if ($response_code !== 200) {
            vedmg_log_error('GOOGLE_SYNC', 'API returned non-200 status', array(
                'status_code' => $response_code,
                'body' => $response_body
            ));
            return false;
        }

        $courses_data = json_decode($response_body, true);

        if (json_last_error() !== JSON_ERROR_NONE) {
            vedmg_log_error('GOOGLE_SYNC', 'Invalid JSON response', array(
                'json_error' => json_last_error_msg(),
                'body' => $response_body
            ));
            return false;
        }

        // Fetch calendar IDs for each course
        $courses_with_calendars = self::fetch_calendar_ids_for_courses($instructor_email, $courses_data);

        return $courses_with_calendars;
    }

    /**
     * Fetch calendar IDs for courses from Google Calendar API
     *
     * @param string $instructor_email Instructor email address
     * @param array $courses Array of courses from Google Classroom
     * @return array Courses with calendar IDs
     */
    private static function fetch_calendar_ids_for_courses($instructor_email, $courses) {
        $calendar_api_url = 'https://gclassroom-839391304260.us-central1.run.app/list_calendar';
        $api_key = 'G$$gle@VedMG!@#';

        vedmg_log_info('GOOGLE_SYNC', 'Fetching calendar IDs for instructor: ' . $instructor_email);

        $post_data = json_encode([
            'instructor_name' => $instructor_email
        ]);

        $response = wp_remote_post($calendar_api_url, array(
            'timeout' => 30,
            'headers' => array(
                'Content-Type' => 'application/json',
                'x-api-key' => $api_key,
                'Content-Length' => strlen($post_data)
            ),
            'body' => $post_data
        ));

        if (is_wp_error($response)) {
            vedmg_log_error('GOOGLE_SYNC', 'Calendar API request failed', $response->get_error_message());
            return $courses; // Return courses without calendar IDs
        }

        $response_code = wp_remote_retrieve_response_code($response);
        $response_body = wp_remote_retrieve_body($response);

        if ($response_code !== 200) {
            vedmg_log_error('GOOGLE_SYNC', 'Calendar API returned non-200 status', array(
                'status_code' => $response_code,
                'body' => $response_body
            ));
            return $courses; // Return courses without calendar IDs
        }

        $calendars_data = json_decode($response_body, true);

        if (json_last_error() !== JSON_ERROR_NONE) {
            vedmg_log_error('GOOGLE_SYNC', 'Invalid JSON response from calendar API', array(
                'json_error' => json_last_error_msg(),
                'body' => $response_body
            ));
            return $courses; // Return courses without calendar IDs
        }

        // Match courses with calendars by name
        foreach ($courses as &$course) {
            $course['calendar_id'] = null; // Default to null

            foreach ($calendars_data as $calendar) {
                if (isset($calendar['summary']) && isset($calendar['id'])) {
                    // Try exact match first
                    if (trim($calendar['summary']) === trim($course['name'])) {
                        $course['calendar_id'] = $calendar['id'];
                        vedmg_log_info('GOOGLE_SYNC', 'Exact calendar match found', array(
                            'course_name' => $course['name'],
                            'calendar_id' => $calendar['id']
                        ));
                        break;
                    }

                    // Try partial match (case insensitive)
                    if (stripos($calendar['summary'], trim($course['name'])) !== false ||
                        stripos(trim($course['name']), $calendar['summary']) !== false) {
                        $course['calendar_id'] = $calendar['id'];
                        vedmg_log_info('GOOGLE_SYNC', 'Partial calendar match found', array(
                            'course_name' => $course['name'],
                            'calendar_summary' => $calendar['summary'],
                            'calendar_id' => $calendar['id']
                        ));
                        break;
                    }
                }
            }

            if (!$course['calendar_id']) {
                vedmg_log_info('GOOGLE_SYNC', 'No calendar found for course: ' . $course['name']);
            }
        }

        return $courses;
    }

    /**
     * Fallback method to sync calendar IDs using Google Calendar API
     * Used when Google Classroom API doesn't provide calendar IDs
     *
     * @param string $instructor_email Instructor email address
     * @return array Sync results
     */
    private static function fallback_sync_calendar_ids($instructor_email) {
        global $wpdb;

        $courses_table = $wpdb->prefix . 'vedmg_courses';
        $calendar_api_url = 'https://gclassroom-839391304260.us-central1.run.app/list_calendar';
        $api_key = 'G$$gle@VedMG!@#';

        vedmg_log_info('GOOGLE_SYNC', 'Running fallback calendar ID sync for: ' . $instructor_email);

        // Get courses without calendar IDs
        $courses_without_calendar = $wpdb->get_results($wpdb->prepare(
            "SELECT course_id, course_name, calendar_id
             FROM $courses_table
             WHERE instructor_email = %s
             AND (calendar_id IS NULL OR calendar_id = '')",
            $instructor_email
        ));

        if (empty($courses_without_calendar)) {
            vedmg_log_info('GOOGLE_SYNC', 'No courses need fallback calendar ID sync');
            return array('updated' => 0, 'total' => 0);
        }

        // Fetch calendars from Google Calendar API
        $response = wp_remote_post($calendar_api_url, array(
            'timeout' => 30,
            'headers' => array(
                'Content-Type' => 'application/json',
                'x-api-key' => $api_key
            ),
            'body' => json_encode(array('instructor_name' => $instructor_email))
        ));

        if (is_wp_error($response)) {
            vedmg_log_error('GOOGLE_SYNC', 'Fallback calendar API request failed', $response->get_error_message());
            return array('updated' => 0, 'total' => count($courses_without_calendar), 'error' => $response->get_error_message());
        }

        $response_code = wp_remote_retrieve_response_code($response);
        $response_body = wp_remote_retrieve_body($response);

        if ($response_code !== 200) {
            vedmg_log_error('GOOGLE_SYNC', 'Fallback calendar API returned error', array(
                'status_code' => $response_code,
                'body' => $response_body
            ));
            return array('updated' => 0, 'total' => count($courses_without_calendar), 'error' => 'API error: ' . $response_code);
        }

        $calendars_data = json_decode($response_body, true);

        if (json_last_error() !== JSON_ERROR_NONE) {
            vedmg_log_error('GOOGLE_SYNC', 'Invalid JSON response from fallback calendar API');
            return array('updated' => 0, 'total' => count($courses_without_calendar), 'error' => 'Invalid JSON response');
        }

        $updated_count = 0;

        // Match courses with calendars by name
        foreach ($courses_without_calendar as $course) {
            foreach ($calendars_data as $calendar) {
                if (isset($calendar['summary']) && isset($calendar['id'])) {
                    // Try exact match first
                    if (trim($calendar['summary']) === trim($course->course_name)) {
                        $update_result = $wpdb->update(
                            $courses_table,
                            array(
                                'calendar_id' => $calendar['id'],
                                'updated_date' => current_time('mysql')
                            ),
                            array('course_id' => $course->course_id),
                            array('%s', '%s'),
                            array('%d')
                        );

                        if ($update_result !== false) {
                            $updated_count++;
                            vedmg_log_info('GOOGLE_SYNC', 'Fallback calendar ID updated', array(
                                'course_name' => $course->course_name,
                                'calendar_id' => $calendar['id'],
                                'source' => 'Google Calendar API (fallback)'
                            ));
                        }
                        break;
                    }

                    // Try partial match
                    if (stripos($calendar['summary'], trim($course->course_name)) !== false ||
                        stripos(trim($course->course_name), $calendar['summary']) !== false) {
                        $update_result = $wpdb->update(
                            $courses_table,
                            array(
                                'calendar_id' => $calendar['id'],
                                'updated_date' => current_time('mysql')
                            ),
                            array('course_id' => $course->course_id),
                            array('%s', '%s'),
                            array('%d')
                        );

                        if ($update_result !== false) {
                            $updated_count++;
                            vedmg_log_info('GOOGLE_SYNC', 'Fallback calendar ID updated (partial match)', array(
                                'course_name' => $course->course_name,
                                'calendar_summary' => $calendar['summary'],
                                'calendar_id' => $calendar['id'],
                                'source' => 'Google Calendar API (fallback)'
                            ));
                        }
                        break;
                    }
                }
            }
        }

        return array(
            'updated' => $updated_count,
            'total' => count($courses_without_calendar)
        );
    }

    /**
     * Enhanced match and update courses using hybrid approach: Google Classroom ID + Name matching
     * 1. First tries exact Google Classroom ID matching for maximum accuracy
     * 2. Falls back to single-word name matching for courses without Google IDs
     * 3. Never creates new courses - only updates existing ones
     *
     * @param array $all_courses All courses from database (courses page)
     * @param array $google_courses Courses from Google Classroom API
     * @param int $instructor_id Selected instructor ID (for attribution)
     * @param string $instructor_name Selected instructor name (for attribution)
     * @param string $api_email Email used for Google Classroom API
     * @return array Match results
     */
    private static function enhanced_match_and_update_courses($all_courses, $google_courses, $instructor_id, $instructor_name, $api_email) {
        global $wpdb;
        
        $courses_table = $wpdb->prefix . 'vedmg_courses';
        $matches = 0;
        $updated = 0;
        $created = 0;
        $details = array();
        $processed_google_courses = array();
        $google_classroom_details = array(); // Track all Google Classroom courses found
        
        // First, catalog all Google Classroom courses for feedback
        foreach ($google_courses as $google_course) {
            $google_classroom_details[] = array(
                'name' => $google_course['name'],
                'id' => $google_course['id'],
                'matched' => false,
                'match_reason' => ''
            );
        }
        
        $processed_db_courses = array();

        vedmg_log_info('GOOGLE_SYNC', 'Starting hybrid matching: Phase 1 - Google Classroom ID matching');

        // Phase 1A: Exact Google Classroom ID matching (highest priority)
        foreach ($all_courses as $db_course) {
            // Skip if this DB course was already matched
            if (in_array($db_course->course_id, $processed_db_courses)) {
                continue;
            }

            // Only process courses that have a Google Classroom ID
            if (empty($db_course->google_classroom_id)) {
                continue;
            }

            foreach ($google_courses as $index => $google_course) {
                $google_course_id = $google_course['id'];

                // Skip if this Google course was already matched
                if (in_array($google_course_id, $processed_google_courses)) {
                    continue;
                }

                // Check for exact Google Classroom ID match
                if ($db_course->google_classroom_id === $google_course_id) {
                    // Exact ID match found!
                    $can_update = ($db_course->instructor_id == $instructor_id);

                    if ($can_update) {
                        // Update database with Google Classroom data including calendar ID
                        $update_data = array(
                            'google_classroom_link' => $google_course['alternateLink'] ?? '',
                            'classroom_status' => 'active',
                            'course_description' => $google_course['descriptionHeading'] ?? $db_course->course_name,
                            'updated_date' => current_time('mysql')
                        );

                        // Check and update calendar ID from Google Classroom API response
                        $calendar_id_updated = false;
                        if (isset($google_course['calendarId']) && !empty($google_course['calendarId'])) {
                            // Calendar ID is present in Google Classroom response
                            if ($db_course->calendar_id !== $google_course['calendarId']) {
                                $update_data['calendar_id'] = $google_course['calendarId'];
                                $calendar_id_updated = true;
                                vedmg_log_info('GOOGLE_SYNC', 'Calendar ID updated from Google Classroom API', array(
                                    'course_name' => $db_course->course_name,
                                    'old_calendar_id' => $db_course->calendar_id ?: 'NULL',
                                    'new_calendar_id' => $google_course['calendarId'],
                                    'source' => 'Google Classroom API'
                                ));
                            } else {
                                vedmg_log_info('GOOGLE_SYNC', 'Calendar ID already correct from Google Classroom API', array(
                                    'course_name' => $db_course->course_name,
                                    'calendar_id' => $google_course['calendarId']
                                ));
                            }
                        } else if (empty($db_course->calendar_id)) {
                            // No calendar ID in Google Classroom response and none in database - use fallback
                            vedmg_log_info('GOOGLE_SYNC', 'No calendar ID in Google Classroom API, will use fallback method', array(
                                'course_name' => $db_course->course_name
                            ));
                        }

                        $update_result = $wpdb->update(
                            $courses_table,
                            $update_data,
                            array('course_id' => $db_course->course_id),
                            array_fill(0, count($update_data), '%s'),
                            array('%d')
                        );

                        if ($update_result !== false) {
                            $matches++;
                            $updated++;
                            $processed_google_courses[] = $google_course_id;
                            $processed_db_courses[] = $db_course->course_id;

                            // Mark this Google Classroom course as matched
                            $google_classroom_details[$index]['matched'] = true;
                            $google_classroom_details[$index]['match_reason'] = "Matched with course '{$db_course->course_name}' by {$db_course->instructor_name} (exact Google Classroom ID)";

                            $details[] = array(
                                'action' => 'updated_by_id',
                                'db_course' => $db_course->course_name,
                                'google_course' => $google_course['name'],
                                'google_classroom_id' => $google_course['id'],
                                'matched_word' => 'Exact Google Classroom ID',
                                'status_change' => $db_course->classroom_status . ' → active',
                                'attributed_to' => $instructor_name,
                                'course_instructor' => $db_course->instructor_name
                            );

                            vedmg_log_info('GOOGLE_SYNC', 'Course matched by ID: ' . $db_course->course_name . ' → ' . $google_course['name'] . ' (ID: ' . $google_course_id . ')');
                        }
                    } else {
                        // Course matched but belongs to different instructor
                        $google_classroom_details[$index]['matched'] = true;
                        $google_classroom_details[$index]['match_reason'] = "Matched with course '{$db_course->course_name}' by exact ID but belongs to {$db_course->instructor_name} (cannot update)";
                        $processed_google_courses[] = $google_course_id;
                        $processed_db_courses[] = $db_course->course_id;
                    }

                    break; // Move to next DB course after finding exact match
                }
            }
        }

        vedmg_log_info('GOOGLE_SYNC', 'Phase 1A complete: ' . $matches . ' exact ID matches. Starting Phase 1B - Name matching');

        // Phase 1B: Name-based matching for remaining courses
        foreach ($all_courses as $db_course) {
            // Skip if this DB course was already matched by ID
            if (in_array($db_course->course_id, $processed_db_courses)) {
                continue;
            }

            foreach ($google_courses as $index => $google_course) {
                $google_course_id = $google_course['id'];

                // Skip if this Google course was already matched
                if (in_array($google_course_id, $processed_google_courses)) {
                    continue;
                }

                // Use enhanced single-word matching
                $match_result = self::enhanced_single_word_match($db_course->course_name, $google_course['name']);

                if ($match_result['match']) {
                    // Check if this course belongs to the selected instructor (only they can update courses)
                    $can_update = ($db_course->instructor_id == $instructor_id);

                    if ($can_update) {
                        // Update database with Google Classroom data (including the Google ID)
                        $update_data = array(
                            'google_classroom_id' => $google_course['id'], // Store the Google ID for future exact matching
                            'google_classroom_link' => $google_course['alternateLink'] ?? '',
                            'classroom_status' => 'active',
                            'course_description' => $google_course['descriptionHeading'] ?? $db_course->course_name,
                            'updated_date' => current_time('mysql')
                        );

                        // Check and update calendar ID from Google Classroom API response
                        if (isset($google_course['calendarId']) && !empty($google_course['calendarId'])) {
                            // Calendar ID is present in Google Classroom response
                            if ($db_course->calendar_id !== $google_course['calendarId']) {
                                $update_data['calendar_id'] = $google_course['calendarId'];
                                vedmg_log_info('GOOGLE_SYNC', 'Calendar ID updated from Google Classroom API (name match)', array(
                                    'course_name' => $db_course->course_name,
                                    'old_calendar_id' => $db_course->calendar_id ?: 'NULL',
                                    'new_calendar_id' => $google_course['calendarId'],
                                    'source' => 'Google Classroom API',
                                    'match_type' => 'name_based'
                                ));
                            } else {
                                vedmg_log_info('GOOGLE_SYNC', 'Calendar ID already correct from Google Classroom API (name match)', array(
                                    'course_name' => $db_course->course_name,
                                    'calendar_id' => $google_course['calendarId']
                                ));
                            }
                        } else if (empty($db_course->calendar_id)) {
                            // No calendar ID in Google Classroom response and none in database - use fallback
                            vedmg_log_info('GOOGLE_SYNC', 'No calendar ID in Google Classroom API, will use fallback method (name match)', array(
                                'course_name' => $db_course->course_name
                            ));
                        }

                        $update_result = $wpdb->update(
                            $courses_table,
                            $update_data,
                            array('course_id' => $db_course->course_id),
                            array_fill(0, count($update_data), '%s'),
                            array('%d')
                        );

                        if ($update_result !== false) {
                            $matches++;
                            $updated++;
                            $processed_google_courses[] = $google_course_id;
                            $processed_db_courses[] = $db_course->course_id;

                            // Mark this Google Classroom course as matched
                            $google_classroom_details[$index]['matched'] = true;
                            $google_classroom_details[$index]['match_reason'] = "Matched with course '{$db_course->course_name}' by {$db_course->instructor_name} (word: '{$match_result['matched_word']}')";

                            $details[] = array(
                                'action' => 'updated_by_name',
                                'db_course' => $db_course->course_name,
                                'google_course' => $google_course['name'],
                                'google_classroom_id' => $google_course['id'],
                                'matched_word' => $match_result['matched_word'],
                                'status_change' => $db_course->classroom_status . ' → active',
                                'attributed_to' => $instructor_name,
                                'course_instructor' => $db_course->instructor_name
                            );

                            vedmg_log_info('GOOGLE_SYNC', 'Course matched by name: ' . $db_course->course_name . ' → ' . $google_course['name'] . ' (matched on: ' . $match_result['matched_word'] . ')');
                        }
                    } else {
                        // Course matched but belongs to different instructor
                        $google_classroom_details[$index]['matched'] = true;
                        $google_classroom_details[$index]['match_reason'] = "Matched with course '{$db_course->course_name}' but belongs to {$db_course->instructor_name} (cannot update)";
                        $processed_google_courses[] = $google_course_id; // Mark as processed to avoid duplicate entries
                        $processed_db_courses[] = $db_course->course_id;
                    }

                    break; // Move to next DB course after finding a match
                }
            }
        }
        
        // Phase 2: Log unmatched Google Classroom courses (but don't create them)
        $unmatched_courses = array();
        foreach ($google_courses as $google_course) {
            $google_course_id = $google_course['id'];

            // Skip if this Google course was already matched
            if (in_array($google_course_id, $processed_google_courses)) {
                continue;
            }

            // Log unmatched course for reporting
            $unmatched_courses[] = $google_course['name'];
            vedmg_log_info('GOOGLE_SYNC', 'Unmatched Google Classroom course (not creating): ' . $google_course['name']);
        }

        // Add unmatched courses info to details if any
        if (!empty($unmatched_courses)) {
            $details[] = array(
                'action' => 'skipped',
                'db_course' => 'N/A',
                'google_course' => 'Unmatched courses: ' . implode(', ', $unmatched_courses),
                'google_classroom_id' => 'N/A',
                'matched_word' => 'No match found',
                'status_change' => 'skipped (no LMS course to match)',
                'attributed_to' => $instructor_name
            );
        }
        
        return array(
            'matches' => $matches,
            'updated' => $updated,
            'created' => $created,
            'details' => $details,
            'google_classroom_details' => $google_classroom_details, // Include all Google Classroom courses found
            'unmatched_google_courses' => $unmatched_courses
        );
    }
    
    /**
     * Enhanced single-word matching between LMS and Google Classroom courses
     * If any single meaningful word matches, consider courses the same
     * 
     * @param string $lms_course_name LMS course name
     * @param string $google_course_name Google Classroom course name
     * @return array Match result with details
     */
    private static function enhanced_single_word_match($lms_course_name, $google_course_name) {
        // Convert to lowercase and clean
        $lms_name = strtolower(trim($lms_course_name));
        $google_name = strtolower(trim($google_course_name));
        
        // Remove common words that don't help with matching
        $common_words = ['the', 'of', 'and', 'a', 'an', 'in', 'on', 'at', 'to', 'for', 'with', 'by', 'course', 'class', 'introduction', 'basic', 'advanced'];
        
        // Extract meaningful words (length > 2)
        $lms_words = array_filter(explode(' ', $lms_name), function($word) use ($common_words) {
            return strlen(trim($word)) > 2 && !in_array(trim($word), $common_words);
        });
        
        $google_words = array_filter(explode(' ', $google_name), function($word) use ($common_words) {
            return strlen(trim($word)) > 2 && !in_array(trim($word), $common_words);
        });
        
        // Clean up words
        $lms_words = array_map('trim', $lms_words);
        $google_words = array_map('trim', $google_words);
        
        // Check for any single word match
        foreach ($lms_words as $lms_word) {
            foreach ($google_words as $google_word) {
                if ($lms_word === $google_word || 
                    strpos($lms_word, $google_word) !== false || 
                    strpos($google_word, $lms_word) !== false) {
                    return array(
                        'match' => true,
                        'matched_word' => $lms_word . ' ↔ ' . $google_word,
                        'confidence' => 'high'
                    );
                }
            }
        }
        
        return array(
            'match' => false,
            'matched_word' => null,
            'confidence' => 'none'
        );
    }

    /**
     * Handle cleanup instructor sync AJAX request
     * This function performs a comprehensive cleanup of the instructor sync table
     */
    public static function handle_cleanup_instructor_sync_ajax() {
        // Verify nonce for security
        if (!wp_verify_nonce($_POST['nonce'], 'vedmg_classroom_nonce')) {
            wp_send_json_error('Security check failed');
            return;
        }

        // Check user permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
            return;
        }

        vedmg_log_admin_action('Started instructor sync table cleanup');

        try {
            global $wpdb;
            $sync_table = $wpdb->prefix . 'vedmg_instructor_sync';

            $deleted_count = 0;
            $duplicate_count = 0;

            // Step 1: Get all current WordPress instructor users
            $instructor_users = get_users(array(
                'role__in' => array('stm_lms_instructor', 'instructor', 'administrator'),
                'fields' => array('ID', 'user_email'),
                'meta_query' => array(
                    'relation' => 'OR',
                    array(
                        'key' => 'wp_capabilities',
                        'value' => 'stm_lms_instructor',
                        'compare' => 'LIKE'
                    ),
                    array(
                        'key' => 'wp_capabilities',
                        'value' => 'instructor',
                        'compare' => 'LIKE'
                    )
                )
            ));

            $existing_user_ids = array_map(function($user) { return $user->ID; }, $instructor_users);

            // Step 2: Remove entries for deleted WordPress users
            $synced_instructors = $wpdb->get_results(
                "SELECT wordpress_user_id, instructor_name, instructor_email FROM $sync_table"
            );

            foreach ($synced_instructors as $synced_instructor) {
                if (!in_array($synced_instructor->wordpress_user_id, $existing_user_ids)) {
                    $delete_result = $wpdb->delete(
                        $sync_table,
                        array('wordpress_user_id' => $synced_instructor->wordpress_user_id),
                        array('%d')
                    );

                    if ($delete_result !== false) {
                        $deleted_count++;
                        vedmg_log_info('INSTRUCTOR_SYNC', "Cleanup: Removed deleted instructor: {$synced_instructor->instructor_name} (ID: {$synced_instructor->wordpress_user_id})");
                    }
                }
            }

            // Step 3: Remove duplicate entries (keep the most recent one for each email)
            $duplicate_emails = $wpdb->get_results("
                SELECT instructor_email, COUNT(*) as count
                FROM $sync_table
                GROUP BY instructor_email
                HAVING count > 1
            ");

            foreach ($duplicate_emails as $dup_email) {
                // Get all records for this email, ordered by last_synced_date DESC
                $records = $wpdb->get_results($wpdb->prepare("
                    SELECT * FROM $sync_table
                    WHERE instructor_email = %s
                    ORDER BY last_synced_date DESC, created_date DESC
                ", $dup_email->instructor_email));

                // Keep the first (most recent) record, delete the rest
                for ($i = 1; $i < count($records); $i++) {
                    $delete_result = $wpdb->delete(
                        $sync_table,
                        array('wordpress_user_id' => $records[$i]->wordpress_user_id),
                        array('%d')
                    );

                    if ($delete_result !== false) {
                        $duplicate_count++;
                        vedmg_log_info('INSTRUCTOR_SYNC', "Cleanup: Removed duplicate instructor: {$records[$i]->instructor_name} (ID: {$records[$i]->wordpress_user_id})");
                    }
                }
            }

            $total_removed = $deleted_count + $duplicate_count;
            $message = "Cleanup completed! {$total_removed} entries removed ({$deleted_count} deleted users, {$duplicate_count} duplicates).";

            $response_data = array(
                'message' => $message,
                'deleted_count' => $deleted_count,
                'duplicate_count' => $duplicate_count,
                'total_removed' => $total_removed
            );

            vedmg_log_admin_action('Instructor sync cleanup completed', "Deleted: {$deleted_count}, Duplicates: {$duplicate_count}");

            wp_send_json_success($response_data);

        } catch (Exception $e) {
            vedmg_log_error('INSTRUCTOR_SYNC', 'Instructor sync cleanup failed', $e->getMessage());
            wp_send_json_error('Cleanup failed: ' . $e->getMessage());
        }
    }

    /**
     * Handle instructor sync status verification AJAX request
     */
    public static function handle_verify_instructor_sync_status_ajax() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'vedmg_classroom_nonce')) {
            wp_send_json_error('Security check failed');
            return;
        }
        
        // Check permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
            return;
        }
        
        $instructor_email = sanitize_email($_POST['instructor_email']);
        
        if (empty($instructor_email)) {
            wp_send_json_error('Instructor email is required');
            return;
        }
        
        global $wpdb;
        $sync_table = $wpdb->prefix . 'vedmg_instructor_sync';
        
        $instructor = $wpdb->get_row($wpdb->prepare("
            SELECT 
                google_sync_status,
                last_google_sync_date,
                google_classroom_count,
                updated_date
            FROM $sync_table 
            WHERE instructor_email = %s
        ", $instructor_email));
        
        if ($instructor) {
            wp_send_json_success(array(
                'google_sync_status' => $instructor->google_sync_status,
                'last_google_sync_date' => $instructor->last_google_sync_date,
                'google_classroom_count' => $instructor->google_classroom_count,
                'updated_date' => $instructor->updated_date,
                'timestamp' => time()
            ));
        } else {
            wp_send_json_error('Instructor not found');
        }
    }

    /**
     * Handle feature session AJAX request
     */
    public static function handle_feature_session_ajax() {
        // Verify nonce for security
        if (!wp_verify_nonce($_POST['nonce'], 'vedmg_classroom_ajax')) {
            wp_send_json_error('Security check failed');
            return;
        }

        // Check user capabilities
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
            return;
        }

        try {
            $session_id = intval($_POST['session_id']);

            if (!$session_id) {
                wp_send_json_error('Invalid session ID');
                return;
            }

            vedmg_log_admin_action('Featuring session: ' . $session_id);

            // Feature the session
            $result = VedMG_ClassRoom_Database_Helper::toggle_session_featured($session_id, true);

            if ($result) {
                wp_send_json_success(array(
                    'message' => 'Session featured successfully',
                    'session_id' => $session_id
                ));
            } else {
                wp_send_json_error('Failed to feature session');
            }

        } catch (Exception $e) {
            vedmg_log_error('ADMIN', 'Error featuring session', $e->getMessage());
            wp_send_json_error('Failed to feature session: ' . $e->getMessage());
        }
    }

    /**
     * Handle unfeature session AJAX request
     */
    public static function handle_unfeature_session_ajax() {
        // Verify nonce for security
        if (!wp_verify_nonce($_POST['nonce'], 'vedmg_classroom_ajax')) {
            wp_send_json_error('Security check failed');
            return;
        }

        // Check user capabilities
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
            return;
        }

        try {
            $session_id = intval($_POST['session_id']);

            if (!$session_id) {
                wp_send_json_error('Invalid session ID');
                return;
            }

            vedmg_log_admin_action('Unfeaturing session: ' . $session_id);

            // Unfeature the session
            $result = VedMG_ClassRoom_Database_Helper::toggle_session_featured($session_id, false);

            if ($result) {
                wp_send_json_success(array(
                    'message' => 'Session removed from featured successfully',
                    'session_id' => $session_id
                ));
            } else {
                wp_send_json_error('Failed to remove session from featured');
            }

        } catch (Exception $e) {
            vedmg_log_error('ADMIN', 'Error unfeaturing session', $e->getMessage());
            wp_send_json_error('Failed to unfeature session: ' . $e->getMessage());
        }
    }

    /**
     * Handle get course meeting link AJAX request
     */
    public static function handle_get_course_meeting_link_ajax() {
        // Verify nonce for security
        if (!wp_verify_nonce($_POST['nonce'], 'vedmg_classroom_ajax')) {
            wp_send_json_error('Security check failed');
            return;
        }

        // Check user capabilities
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
            return;
        }

        try {
            $course_id = intval($_POST['course_id']);
            $session_id = intval($_POST['session_id']);

            if (!$course_id) {
                wp_send_json_error('Invalid course ID');
                return;
            }

            vedmg_log_admin_action('Getting meeting link for course: ' . $course_id . ', session: ' . $session_id);

            // Get course data
            global $wpdb;
            $course = $wpdb->get_row($wpdb->prepare(
                "SELECT * FROM {$wpdb->prefix}vedmg_courses WHERE course_id = %d",
                $course_id
            ));

            if (!$course) {
                wp_send_json_error('Course not found');
                return;
            }

            // Get meeting link - prefer session-specific link, fallback to course link
            $meeting_link = '';

            if ($session_id) {
                $session = $wpdb->get_row($wpdb->prepare(
                    "SELECT google_meet_link FROM {$wpdb->prefix}vedmg_class_sessions WHERE session_id = %d",
                    $session_id
                ));

                if ($session && !empty($session->google_meet_link)) {
                    $meeting_link = $session->google_meet_link;
                }
            }

            // Fallback to course meeting link
            if (empty($meeting_link) && !empty($course->meeting_link)) {
                $meeting_link = $course->meeting_link;
            }

            if (empty($meeting_link)) {
                wp_send_json_error('No meeting link available for this course/session');
                return;
            }

            wp_send_json_success(array(
                'meeting_link' => $meeting_link,
                'course_name' => $course->course_name,
                'instructor_name' => $course->instructor_name
            ));

        } catch (Exception $e) {
            vedmg_log_error('ADMIN', 'Error getting course meeting link', $e->getMessage());
            wp_send_json_error('Failed to get meeting link: ' . $e->getMessage());
        }
    }

}

?>
