[2025-09-01 07:59:02] [INFO] [API] API handlers initialized
[2025-09-01 07:59:03] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:59:03] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:59:03] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:59:03] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:59:03] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:59:03] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:59:03] [INFO] [CORE] Starting core initialization
[2025-09-01 07:59:03] [INFO] [ADMIN] Admin interface initialized
[2025-09-01 07:59:03] [INFO] [CORE] Admin functionality loaded
[2025-09-01 07:59:03] [INFO] [CORE] Core initialization completed
[2025-09-01 07:59:04] [INFO] [ADMIN] Settings initialized
[2025-09-01 07:59:04] [INFO] [SCHEDULE_LAB] Schedule lab request received
[2025-09-01 07:59:04] [INFO] [SCHEDULE_LAB] Nonce verification
[2025-09-01 07:59:04] [INFO] [SCHEDULE_LAB] Nonce verification passed
[2025-09-01 07:59:04] [INFO] [SCHEDULE_LAB] Session type: class
[2025-09-01 07:59:04] [INFO] [SCHEDULE_LAB] Starting class session scheduling
[2025-09-01 07:59:04] [INFO] [SCHEDULE_LAB] Processing class-wide session
[2025-09-01 07:59:04] [INFO] [SCHEDULE_LAB] Getting course data for course_id: 68 from table: wp_vedmg_courses
[2025-09-01 07:59:04] [INFO] [SCHEDULE_LAB] Course data retrieved successfully
[2025-09-01 07:59:04] [INFO] [SCHEDULE_LAB] API Request: {
    "calendar_id": "<EMAIL>",
    "user_email": "<EMAIL>",
    "summary": "Live Session - tata motors",
    "location": "On Google Meet",
    "description": "wertyuiop. Please join at https:\/\/meet.google.com\/xfd-hwqc-gvj",
    "start_datetime": "2025-09-02T14:00:00",
    "end_datetime": "2025-09-02T15:00:00",
    "instructor_email": "<EMAIL>",
    "meeting_frequency": "FREQ=WEEKLY;COUNT=1"
}
[2025-09-01 07:59:04] [INFO] [SCHEDULE_LAB] Making real API call to: https://gclassroom-839391304260.us-central1.run.app/share_invite
[2025-09-01 07:59:05] [INFO] [SCHEDULE_LAB] API Response - HTTP: 500, Body: <!doctype html>
<html lang=en>
<title>500 Internal Server Error</title>
<h1>Internal Server Error</h1>
<p>The server encountered an internal error and was unable to complete your request. Either the server is overloaded or there is an error in the application.</p>

[2025-09-01 07:59:05] [ERROR] [SCHEDULE_LAB] API Error | Details: HTTP 500: <!doctype html>
<html lang=en>
<title>500 Internal Server Error</title>
<h1>Internal Server Error</h1>
<p>The server encountered an internal error and was unable to complete your request. Either the server is overloaded or there is an error in the application.</p>

[2025-09-01 07:59:05] [ERROR] [SCHEDULE_LAB] Class-wide session failed for student | Details: <EMAIL>: API returned error: HTTP 500
