[2025-09-01 05:31:32] [INFO] [API] API handlers initialized
[2025-09-01 05:31:32] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 05:31:32] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 05:31:32] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 05:31:32] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 05:31:32] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 05:31:32] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 05:31:32] [INFO] [CORE] Starting core initialization
[2025-09-01 05:31:32] [INFO] [ADMIN] Admin interface initialized
[2025-09-01 05:31:32] [INFO] [CORE] Admin functionality loaded
[2025-09-01 05:31:32] [INFO] [CORE] Core initialization completed
[2025-09-01 05:31:33] [INFO] [API] API handlers initialized
[2025-09-01 05:31:34] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 05:31:34] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 05:31:34] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 05:31:34] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 05:31:34] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 05:31:34] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 05:31:34] [INFO] [CORE] Starting core initialization
[2025-09-01 05:31:34] [INFO] [CORE] Core initialization completed
[2025-09-01 05:31:34] [INFO] [ADMIN] Settings initialized
[2025-09-01 05:32:13] [INFO] [API] API handlers initialized
[2025-09-01 05:32:13] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 05:32:13] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 05:32:13] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 05:32:13] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 05:32:13] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 05:32:13] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 05:32:13] [INFO] [CORE] Starting core initialization
[2025-09-01 05:32:13] [INFO] [CORE] Core initialization completed
[2025-09-01 05:33:33] [INFO] [API] API handlers initialized
[2025-09-01 05:33:33] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 05:33:33] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 05:33:33] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 05:33:33] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 05:33:33] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 05:33:33] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 05:33:33] [INFO] [CORE] Starting core initialization
[2025-09-01 05:33:33] [INFO] [ADMIN] Admin interface initialized
[2025-09-01 05:33:33] [INFO] [CORE] Admin functionality loaded
[2025-09-01 05:33:33] [INFO] [CORE] Core initialization completed
[2025-09-01 05:33:34] [INFO] [API] API handlers initialized
[2025-09-01 05:33:34] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 05:33:34] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 05:33:35] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 05:33:35] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 05:33:35] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 05:33:35] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 05:33:35] [INFO] [CORE] Starting core initialization
[2025-09-01 05:33:35] [INFO] [CORE] Core initialization completed
[2025-09-01 05:33:35] [INFO] [ADMIN] Settings initialized
[2025-09-01 05:35:17] [INFO] [API] API handlers initialized
[2025-09-01 05:35:17] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 05:35:17] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 05:35:17] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 05:35:17] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 05:35:17] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 05:35:17] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 05:35:17] [INFO] [CORE] Starting core initialization
[2025-09-01 05:35:17] [INFO] [ADMIN] Admin interface initialized
[2025-09-01 05:35:17] [INFO] [CORE] Admin functionality loaded
[2025-09-01 05:35:17] [INFO] [CORE] Core initialization completed
[2025-09-01 05:35:18] [INFO] [API] API handlers initialized
[2025-09-01 05:35:19] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 05:35:19] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 05:35:19] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 05:35:19] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 05:35:19] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 05:35:19] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 05:35:19] [INFO] [CORE] Starting core initialization
[2025-09-01 05:35:19] [INFO] [CORE] Core initialization completed
[2025-09-01 05:35:19] [INFO] [ADMIN] Settings initialized
[2025-09-01 05:35:24] [INFO] [API] API handlers initialized
[2025-09-01 05:35:24] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 05:35:24] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 05:35:24] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 05:35:24] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 05:35:24] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 05:35:24] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 05:35:24] [INFO] [CORE] Starting core initialization
[2025-09-01 05:35:24] [INFO] [ADMIN] Admin interface initialized
[2025-09-01 05:35:24] [INFO] [CORE] Admin functionality loaded
[2025-09-01 05:35:24] [INFO] [CORE] Core initialization completed
[2025-09-01 05:35:25] [INFO] [ADMIN] Admin menu created
[2025-09-01 05:35:25] [INFO] [ADMIN] Settings initialized
[2025-09-01 05:35:25] [INFO] [ADMIN] Admin assets enqueued for page: sessions
[2025-09-01 05:35:25] [ACTION] [ADMIN] Admin action: Accessed sessions page | User: localadmin (ID: 27)
[2025-09-01 05:35:25] [ACTION] [ADMIN] Admin action: Viewed class sessions page | User: localadmin (ID: 27)
[2025-09-01 05:35:25] [INFO] [DATABASE] Auto-updating session statuses. Current time: 2025-09-01 05:35:25
[2025-09-01 05:35:25] [DATABASE] [DB] Database SELECT on table: vedmg_class_sessions | Details: Retrieved 10 sessions (page 1 of 6)
[2025-09-01 05:35:26] [DATABASE] [DB] Database SELECT on table: classroom_options | Details: Retrieved 26 classroom options from database
[2025-09-01 05:35:26] [DATABASE] [DB] Database SELECT on table: vedmg_courses | Details: Retrieved 52 courses (page 1 of 1)
[2025-09-01 05:35:26] [DATABASE] [DB] Database SELECT on table: vedmg_class_sessions | Details: Retrieved 0 featured sessions
[2025-09-01 05:36:28] [INFO] [API] API handlers initialized
[2025-09-01 05:36:28] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 05:36:28] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 05:36:28] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 05:36:28] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 05:36:28] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 05:36:28] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 05:36:28] [INFO] [CORE] Starting core initialization
[2025-09-01 05:36:28] [INFO] [ADMIN] Admin interface initialized
[2025-09-01 05:36:28] [INFO] [CORE] Admin functionality loaded
[2025-09-01 05:36:28] [INFO] [CORE] Core initialization completed
[2025-09-01 05:36:29] [INFO] [API] API handlers initialized
[2025-09-01 05:36:29] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 05:36:29] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 05:36:29] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 05:36:29] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 05:36:29] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 05:36:29] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 05:36:29] [INFO] [CORE] Starting core initialization
[2025-09-01 05:36:29] [INFO] [CORE] Core initialization completed
[2025-09-01 05:36:30] [INFO] [ADMIN] Settings initialized
[2025-09-01 05:38:29] [INFO] [API] API handlers initialized
[2025-09-01 05:38:29] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 05:38:29] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 05:38:29] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 05:38:29] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 05:38:29] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 05:38:29] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 05:38:29] [INFO] [CORE] Starting core initialization
[2025-09-01 05:38:29] [INFO] [ADMIN] Admin interface initialized
[2025-09-01 05:38:29] [INFO] [CORE] Admin functionality loaded
[2025-09-01 05:38:29] [INFO] [CORE] Core initialization completed
[2025-09-01 05:38:30] [INFO] [API] API handlers initialized
[2025-09-01 05:38:30] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 05:38:30] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 05:38:30] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 05:38:30] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 05:38:30] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 05:38:30] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 05:38:30] [INFO] [CORE] Starting core initialization
[2025-09-01 05:38:30] [INFO] [CORE] Core initialization completed
[2025-09-01 05:38:31] [INFO] [ADMIN] Settings initialized
[2025-09-01 05:40:30] [INFO] [API] API handlers initialized
[2025-09-01 05:40:30] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 05:40:30] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 05:40:30] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 05:40:30] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 05:40:30] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 05:40:30] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 05:40:30] [INFO] [CORE] Starting core initialization
[2025-09-01 05:40:30] [INFO] [ADMIN] Admin interface initialized
[2025-09-01 05:40:30] [INFO] [CORE] Admin functionality loaded
[2025-09-01 05:40:30] [INFO] [CORE] Core initialization completed
[2025-09-01 05:40:31] [INFO] [API] API handlers initialized
[2025-09-01 05:40:31] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 05:40:31] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 05:40:31] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 05:40:31] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 05:40:31] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 05:40:31] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 05:40:31] [INFO] [CORE] Starting core initialization
[2025-09-01 05:40:31] [INFO] [CORE] Core initialization completed
[2025-09-01 05:40:32] [INFO] [ADMIN] Settings initialized
[2025-09-01 05:42:31] [INFO] [API] API handlers initialized
[2025-09-01 05:42:31] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 05:42:31] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 05:42:31] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 05:42:31] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 05:42:31] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 05:42:31] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 05:42:31] [INFO] [CORE] Starting core initialization
[2025-09-01 05:42:31] [INFO] [ADMIN] Admin interface initialized
[2025-09-01 05:42:31] [INFO] [CORE] Admin functionality loaded
[2025-09-01 05:42:31] [INFO] [CORE] Core initialization completed
[2025-09-01 05:42:32] [INFO] [API] API handlers initialized
[2025-09-01 05:42:32] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 05:42:32] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 05:42:32] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 05:42:32] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 05:42:32] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 05:42:32] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 05:42:32] [INFO] [CORE] Starting core initialization
[2025-09-01 05:42:32] [INFO] [CORE] Core initialization completed
[2025-09-01 05:42:33] [INFO] [ADMIN] Settings initialized
[2025-09-01 07:03:55] [INFO] [API] API handlers initialized
[2025-09-01 07:04:34] [INFO] [API] API handlers initialized
[2025-09-01 07:04:35] [INFO] [API] API handlers initialized
[2025-09-01 07:04:36] [INFO] [API] API handlers initialized
[2025-09-01 07:04:39] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:04:39] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:04:39] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:04:39] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:04:39] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:04:39] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:04:39] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:04:39] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:04:39] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:04:39] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:04:39] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:04:39] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:04:39] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:04:39] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:04:39] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:04:39] [INFO] [CORE] Starting core initialization
[2025-09-01 07:04:39] [INFO] [CORE] Starting core initialization
[2025-09-01 07:04:39] [INFO] [CORE] Core initialization completed
[2025-09-01 07:04:39] [INFO] [CORE] Core initialization completed
[2025-09-01 07:04:39] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:04:39] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:04:39] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:04:39] [INFO] [CORE] Starting core initialization
[2025-09-01 07:04:39] [INFO] [CORE] Core initialization completed
[2025-09-01 07:04:40] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:04:40] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:04:40] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:04:40] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:04:40] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:04:40] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:04:40] [INFO] [CORE] Starting core initialization
[2025-09-01 07:04:40] [INFO] [ADMIN] Admin interface initialized
[2025-09-01 07:04:40] [INFO] [CORE] Admin functionality loaded
[2025-09-01 07:04:40] [INFO] [CORE] Core initialization completed
[2025-09-01 07:05:32] [INFO] [API] API handlers initialized
[2025-09-01 07:05:32] [INFO] [API] API handlers initialized
[2025-09-01 07:05:32] [INFO] [API] API handlers initialized
[2025-09-01 07:05:33] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:05:33] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:05:33] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:05:33] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:05:33] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:05:33] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:05:33] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:05:33] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:05:33] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:05:33] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:05:33] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:05:33] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:05:33] [INFO] [CORE] Starting core initialization
[2025-09-01 07:05:33] [INFO] [CORE] Core initialization completed
[2025-09-01 07:05:33] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:05:33] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:05:33] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:05:33] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:05:33] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:05:33] [INFO] [CORE] Starting core initialization
[2025-09-01 07:05:33] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:05:33] [INFO] [CORE] Core initialization completed
[2025-09-01 07:05:33] [INFO] [CORE] Starting core initialization
[2025-09-01 07:05:33] [INFO] [CORE] Core initialization completed
[2025-09-01 07:05:49] [INFO] [API] API handlers initialized
[2025-09-01 07:05:49] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:05:49] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:05:49] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:05:49] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:05:49] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:05:49] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:05:49] [INFO] [CORE] Starting core initialization
[2025-09-01 07:05:49] [INFO] [CORE] Core initialization completed
[2025-09-01 07:09:39] [INFO] [API] API handlers initialized
[2025-09-01 07:09:39] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:09:39] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:09:39] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:09:39] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:09:39] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:09:39] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:09:39] [INFO] [CORE] Starting core initialization
[2025-09-01 07:09:39] [INFO] [ADMIN] Admin interface initialized
[2025-09-01 07:09:39] [INFO] [CORE] Admin functionality loaded
[2025-09-01 07:09:39] [INFO] [CORE] Core initialization completed
[2025-09-01 07:09:42] [INFO] [API] API handlers initialized
[2025-09-01 07:09:43] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:09:43] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:09:43] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:09:43] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:09:43] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:09:43] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:09:43] [INFO] [CORE] Starting core initialization
[2025-09-01 07:09:43] [INFO] [CORE] Core initialization completed
[2025-09-01 07:09:44] [INFO] [API] API handlers initialized
[2025-09-01 07:09:44] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:09:44] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:09:44] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:09:44] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:09:44] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:09:44] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:09:44] [INFO] [CORE] Starting core initialization
[2025-09-01 07:09:44] [INFO] [CORE] Core initialization completed
[2025-09-01 07:09:45] [INFO] [API] API handlers initialized
[2025-09-01 07:09:45] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:09:45] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:09:45] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:09:45] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:09:45] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:09:45] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:09:45] [INFO] [CORE] Starting core initialization
[2025-09-01 07:09:45] [INFO] [CORE] Core initialization completed
[2025-09-01 07:09:47] [INFO] [ADMIN] Admin menu created
[2025-09-01 07:09:50] [INFO] [ADMIN] Settings initialized
[2025-09-01 07:09:50] [INFO] [ADMIN] Admin assets enqueued for page: sessions
[2025-09-01 07:09:52] [ACTION] [ADMIN] Admin action: Accessed sessions page | User: localadmin (ID: 27)
[2025-09-01 07:09:52] [ACTION] [ADMIN] Admin action: Viewed class sessions page | User: localadmin (ID: 27)
[2025-09-01 07:09:52] [INFO] [DATABASE] Auto-updating session statuses. Current time: 2025-09-01 07:09:52
[2025-09-01 07:09:52] [DATABASE] [DB] Database SELECT on table: vedmg_class_sessions | Details: Retrieved 10 sessions (page 1 of 6)
[2025-09-01 07:09:52] [DATABASE] [DB] Database SELECT on table: classroom_options | Details: Retrieved 26 classroom options from database
[2025-09-01 07:09:52] [DATABASE] [DB] Database SELECT on table: vedmg_courses | Details: Retrieved 52 courses (page 1 of 1)
[2025-09-01 07:09:52] [DATABASE] [DB] Database SELECT on table: vedmg_class_sessions | Details: Retrieved 0 featured sessions
[2025-09-01 07:10:07] [INFO] [API] API handlers initialized
[2025-09-01 07:10:08] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:10:08] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:10:08] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:10:08] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:10:08] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:10:08] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:10:08] [INFO] [CORE] Starting core initialization
[2025-09-01 07:10:08] [INFO] [ADMIN] Admin interface initialized
[2025-09-01 07:10:08] [INFO] [CORE] Admin functionality loaded
[2025-09-01 07:10:08] [INFO] [CORE] Core initialization completed
[2025-09-01 07:10:08] [INFO] [ADMIN] Settings initialized
[2025-09-01 07:10:09] [INFO] [API] API handlers initialized
[2025-09-01 07:10:09] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:10:09] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:10:09] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:10:09] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:10:09] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:10:09] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:10:09] [INFO] [CORE] Starting core initialization
[2025-09-01 07:10:09] [INFO] [CORE] Core initialization completed
[2025-09-01 07:10:11] [INFO] [API] API handlers initialized
[2025-09-01 07:10:12] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:10:12] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:10:12] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:10:12] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:10:12] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:10:12] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:10:12] [INFO] [CORE] Starting core initialization
[2025-09-01 07:10:12] [INFO] [CORE] Core initialization completed
[2025-09-01 07:10:19] [INFO] [API] API handlers initialized
[2025-09-01 07:10:19] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:10:19] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:10:19] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:10:19] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:10:19] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:10:19] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:10:19] [INFO] [CORE] Starting core initialization
[2025-09-01 07:10:19] [INFO] [ADMIN] Admin interface initialized
[2025-09-01 07:10:19] [INFO] [CORE] Admin functionality loaded
[2025-09-01 07:10:19] [INFO] [CORE] Core initialization completed
[2025-09-01 07:10:20] [INFO] [API] API handlers initialized
[2025-09-01 07:10:21] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:10:21] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:10:21] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:10:21] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:10:21] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:10:21] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:10:21] [INFO] [CORE] Starting core initialization
[2025-09-01 07:10:21] [INFO] [CORE] Core initialization completed
[2025-09-01 07:10:21] [INFO] [ADMIN] Admin menu created
[2025-09-01 07:10:21] [INFO] [ADMIN] Settings initialized
[2025-09-01 07:10:21] [INFO] [ADMIN] Admin assets enqueued for page: courses
[2025-09-01 07:10:21] [ACTION] [ADMIN] Admin action: Accessed courses page | User: localadmin (ID: 27)
[2025-09-01 07:10:22] [ACTION] [ADMIN] Admin action: Viewed courses management page | User: localadmin (ID: 27)
[2025-09-01 07:10:22] [DATABASE] [DB] Database SELECT on table: vedmg_courses | Details: Retrieved 10 courses (page 1 of 6)
[2025-09-01 07:10:22] [DATABASE] [DB] Database SELECT on table: classroom_options | Details: Retrieved 26 classroom options from database
[2025-09-01 07:10:27] [INFO] [API] API handlers initialized
[2025-09-01 07:10:27] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:10:27] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:10:27] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:10:27] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:10:27] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:10:27] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:10:27] [INFO] [CORE] Starting core initialization
[2025-09-01 07:10:27] [INFO] [CORE] Core initialization completed
[2025-09-01 07:10:28] [INFO] [API] API handlers initialized
[2025-09-01 07:10:28] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:10:28] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:10:28] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:10:28] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:10:28] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:10:28] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:10:28] [INFO] [CORE] Starting core initialization
[2025-09-01 07:10:28] [INFO] [CORE] Core initialization completed
[2025-09-01 07:10:30] [INFO] [API] API handlers initialized
[2025-09-01 07:10:30] [INFO] [API] API handlers initialized
[2025-09-01 07:10:30] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:10:30] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:10:30] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:10:30] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:10:30] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:10:30] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:10:30] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:10:30] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:10:30] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:10:30] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:10:30] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:10:30] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:10:30] [INFO] [CORE] Starting core initialization
[2025-09-01 07:10:30] [INFO] [CORE] Starting core initialization
[2025-09-01 07:10:30] [INFO] [CORE] Core initialization completed
[2025-09-01 07:10:30] [INFO] [CORE] Core initialization completed
[2025-09-01 07:10:43] [INFO] [API] API handlers initialized
[2025-09-01 07:10:43] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:10:43] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:10:43] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:10:43] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:10:43] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:10:43] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:10:43] [INFO] [CORE] Starting core initialization
[2025-09-01 07:10:43] [INFO] [CORE] Core initialization completed
[2025-09-01 07:10:44] [INFO] [API] API handlers initialized
[2025-09-01 07:10:44] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:10:44] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:10:44] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:10:44] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:10:44] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:10:44] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:10:44] [INFO] [CORE] Starting core initialization
[2025-09-01 07:10:44] [INFO] [CORE] Core initialization completed
[2025-09-01 07:10:45] [INFO] [MASTERSTUDY] Course 50007 status changed from new to draft
[2025-09-01 07:10:45] [INFO] [MASTERSTUDY] Course save event: 50007 (update: no)
[2025-09-01 07:10:45] [INFO] [MASTERSTUDY] Created new course in VedMG database: 50007 (VedMG ID: 68)
[2025-09-01 07:10:45] [INFO] [CLASSROOM] Triggered classroom creation for course: 68
[2025-09-01 07:10:51] [INFO] [API] API handlers initialized
[2025-09-01 07:10:52] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:10:52] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:10:52] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:10:52] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:10:52] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:10:52] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:10:52] [INFO] [CORE] Starting core initialization
[2025-09-01 07:10:52] [INFO] [CORE] Core initialization completed
[2025-09-01 07:10:52] [INFO] [API] API handlers initialized
[2025-09-01 07:10:53] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:10:53] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:10:53] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:10:53] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:10:53] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:10:53] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:10:53] [INFO] [CORE] Starting core initialization
[2025-09-01 07:10:53] [INFO] [ADMIN] Admin interface initialized
[2025-09-01 07:10:53] [INFO] [CORE] Admin functionality loaded
[2025-09-01 07:10:53] [INFO] [CORE] Core initialization completed
[2025-09-01 07:10:53] [INFO] [API] API handlers initialized
[2025-09-01 07:10:53] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:10:53] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:10:53] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:10:53] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:10:53] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:10:53] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:10:53] [INFO] [CORE] Starting core initialization
[2025-09-01 07:10:53] [INFO] [CORE] Core initialization completed
[2025-09-01 07:10:53] [INFO] [ADMIN] Admin menu created
[2025-09-01 07:10:54] [INFO] [ADMIN] Settings initialized
[2025-09-01 07:10:58] [INFO] [API] API handlers initialized
[2025-09-01 07:10:59] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:10:59] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:10:59] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:10:59] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:10:59] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:10:59] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:10:59] [INFO] [CORE] Starting core initialization
[2025-09-01 07:10:59] [INFO] [ADMIN] Admin interface initialized
[2025-09-01 07:10:59] [INFO] [CORE] Admin functionality loaded
[2025-09-01 07:10:59] [INFO] [CORE] Core initialization completed
[2025-09-01 07:11:00] [INFO] [API] API handlers initialized
[2025-09-01 07:11:00] [INFO] [ADMIN] Settings initialized
[2025-09-01 07:11:00] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:11:00] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:11:00] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:11:00] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:11:00] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:11:00] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:11:00] [INFO] [CORE] Starting core initialization
[2025-09-01 07:11:00] [INFO] [CORE] Core initialization completed
[2025-09-01 07:11:02] [INFO] [API] API handlers initialized
[2025-09-01 07:11:02] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:11:02] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:11:02] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:11:02] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:11:02] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:11:02] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:11:02] [INFO] [CORE] Starting core initialization
[2025-09-01 07:11:02] [INFO] [CORE] Core initialization completed
[2025-09-01 07:11:03] [INFO] [API] API handlers initialized
[2025-09-01 07:11:03] [INFO] [API] API handlers initialized
[2025-09-01 07:11:03] [INFO] [API] API handlers initialized
[2025-09-01 07:11:03] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:11:03] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:11:03] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:11:03] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:11:03] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:11:03] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:11:03] [INFO] [CORE] Starting core initialization
[2025-09-01 07:11:03] [INFO] [CORE] Core initialization completed
[2025-09-01 07:11:03] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:11:03] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:11:03] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:11:03] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:11:03] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:11:03] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:11:03] [INFO] [CORE] Starting core initialization
[2025-09-01 07:11:03] [INFO] [CORE] Core initialization completed
[2025-09-01 07:11:03] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:11:03] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:11:03] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:11:03] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:11:03] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:11:03] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:11:03] [INFO] [CORE] Starting core initialization
[2025-09-01 07:11:03] [INFO] [CORE] Core initialization completed
[2025-09-01 07:11:04] [INFO] [API] API handlers initialized
[2025-09-01 07:11:04] [INFO] [API] API handlers initialized
[2025-09-01 07:11:04] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:11:04] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:11:04] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:11:04] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:11:04] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:11:04] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:11:04] [INFO] [CORE] Starting core initialization
[2025-09-01 07:11:04] [INFO] [CORE] Core initialization completed
[2025-09-01 07:11:04] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:11:04] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:11:04] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:11:04] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:11:04] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:11:04] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:11:04] [INFO] [CORE] Starting core initialization
[2025-09-01 07:11:04] [INFO] [CORE] Core initialization completed
[2025-09-01 07:11:06] [INFO] [API] API handlers initialized
[2025-09-01 07:11:06] [INFO] [API] API handlers initialized
[2025-09-01 07:11:06] [INFO] [API] API handlers initialized
[2025-09-01 07:11:06] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:11:06] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:11:06] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:11:06] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:11:06] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:11:06] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:11:06] [INFO] [CORE] Starting core initialization
[2025-09-01 07:11:06] [INFO] [CORE] Core initialization completed
[2025-09-01 07:11:06] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:11:06] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:11:06] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:11:06] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:11:06] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:11:06] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:11:06] [INFO] [CORE] Starting core initialization
[2025-09-01 07:11:06] [INFO] [CORE] Core initialization completed
[2025-09-01 07:11:06] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:11:06] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:11:06] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:11:06] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:11:06] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:11:06] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:11:06] [INFO] [CORE] Starting core initialization
[2025-09-01 07:11:06] [INFO] [CORE] Core initialization completed
[2025-09-01 07:11:07] [INFO] [API] API handlers initialized
[2025-09-01 07:11:07] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:11:07] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:11:07] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:11:07] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:11:07] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:11:07] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:11:07] [INFO] [CORE] Starting core initialization
[2025-09-01 07:11:07] [INFO] [CORE] Core initialization completed
[2025-09-01 07:11:08] [INFO] [API] API handlers initialized
[2025-09-01 07:11:09] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:11:09] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:11:09] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:11:09] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:11:09] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:11:09] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:11:09] [INFO] [CORE] Starting core initialization
[2025-09-01 07:11:09] [INFO] [CORE] Core initialization completed
[2025-09-01 07:11:09] [INFO] [API] API handlers initialized
[2025-09-01 07:11:09] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:11:09] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:11:09] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:11:09] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:11:09] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:11:09] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:11:09] [INFO] [CORE] Starting core initialization
[2025-09-01 07:11:09] [INFO] [ADMIN] Admin interface initialized
[2025-09-01 07:11:09] [INFO] [CORE] Admin functionality loaded
[2025-09-01 07:11:09] [INFO] [CORE] Core initialization completed
[2025-09-01 07:11:10] [INFO] [API] API handlers initialized
[2025-09-01 07:11:10] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:11:10] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:11:10] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:11:10] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:11:10] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:11:10] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:11:10] [INFO] [CORE] Starting core initialization
[2025-09-01 07:11:10] [INFO] [CORE] Core initialization completed
[2025-09-01 07:11:10] [INFO] [ADMIN] Settings initialized
[2025-09-01 07:11:15] [INFO] [API] API handlers initialized
[2025-09-01 07:11:16] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:11:16] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:11:16] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:11:16] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:11:16] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:11:16] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:11:16] [INFO] [CORE] Starting core initialization
[2025-09-01 07:11:16] [INFO] [CORE] Core initialization completed
[2025-09-01 07:11:17] [INFO] [API] API handlers initialized
[2025-09-01 07:11:17] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:11:17] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:11:17] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:11:17] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:11:17] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:11:17] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:11:17] [INFO] [CORE] Starting core initialization
[2025-09-01 07:11:17] [INFO] [CORE] Core initialization completed
[2025-09-01 07:11:19] [INFO] [API] API handlers initialized
[2025-09-01 07:11:19] [INFO] [API] API handlers initialized
[2025-09-01 07:11:19] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:11:19] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:11:19] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:11:19] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:11:19] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:11:19] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:11:19] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:11:19] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:11:19] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:11:19] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:11:19] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:11:19] [INFO] [CORE] Starting core initialization
[2025-09-01 07:11:19] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:11:19] [INFO] [CORE] Core initialization completed
[2025-09-01 07:11:19] [INFO] [CORE] Starting core initialization
[2025-09-01 07:11:19] [INFO] [ADMIN] Admin interface initialized
[2025-09-01 07:11:19] [INFO] [CORE] Admin functionality loaded
[2025-09-01 07:11:19] [INFO] [CORE] Core initialization completed
[2025-09-01 07:11:20] [INFO] [ADMIN] Settings initialized
[2025-09-01 07:11:21] [INFO] [API] API handlers initialized
[2025-09-01 07:11:21] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:11:21] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:11:21] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:11:21] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:11:21] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:11:21] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:11:21] [INFO] [CORE] Starting core initialization
[2025-09-01 07:11:21] [INFO] [CORE] Core initialization completed
[2025-09-01 07:11:22] [INFO] [API] API handlers initialized
[2025-09-01 07:11:22] [INFO] [API] API handlers initialized
[2025-09-01 07:11:22] [INFO] [API] API handlers initialized
[2025-09-01 07:11:23] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:11:23] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:11:23] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:11:23] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:11:23] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:11:23] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:11:23] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:11:23] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:11:23] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:11:23] [INFO] [CORE] Starting core initialization
[2025-09-01 07:11:23] [INFO] [CORE] Core initialization completed
[2025-09-01 07:11:23] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:11:23] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:11:23] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:11:23] [INFO] [CORE] Starting core initialization
[2025-09-01 07:11:23] [INFO] [ADMIN] Admin interface initialized
[2025-09-01 07:11:23] [INFO] [CORE] Admin functionality loaded
[2025-09-01 07:11:23] [INFO] [CORE] Core initialization completed
[2025-09-01 07:11:23] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:11:23] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:11:23] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:11:23] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:11:23] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:11:23] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:11:23] [INFO] [CORE] Starting core initialization
[2025-09-01 07:11:23] [INFO] [CORE] Core initialization completed
[2025-09-01 07:11:24] [INFO] [ADMIN] Settings initialized
[2025-09-01 07:11:24] [INFO] [API] API handlers initialized
[2025-09-01 07:11:24] [INFO] [API] API handlers initialized
[2025-09-01 07:11:24] [INFO] [API] API handlers initialized
[2025-09-01 07:11:25] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:11:25] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:11:25] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:11:25] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:11:25] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:11:25] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:11:25] [INFO] [CORE] Starting core initialization
[2025-09-01 07:11:25] [INFO] [CORE] Core initialization completed
[2025-09-01 07:11:25] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:11:25] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:11:25] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:11:25] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:11:25] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:11:25] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:11:25] [INFO] [CORE] Starting core initialization
[2025-09-01 07:11:25] [INFO] [CORE] Core initialization completed
[2025-09-01 07:11:25] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:11:25] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:11:25] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:11:25] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:11:25] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:11:25] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:11:25] [INFO] [CORE] Starting core initialization
[2025-09-01 07:11:25] [INFO] [ADMIN] Admin interface initialized
[2025-09-01 07:11:25] [INFO] [CORE] Admin functionality loaded
[2025-09-01 07:11:25] [INFO] [CORE] Core initialization completed
[2025-09-01 07:11:26] [INFO] [API] API handlers initialized
[2025-09-01 07:11:26] [INFO] [ADMIN] Settings initialized
[2025-09-01 07:11:26] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:11:26] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:11:26] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:11:26] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:11:26] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:11:26] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:11:26] [INFO] [CORE] Starting core initialization
[2025-09-01 07:11:26] [INFO] [CORE] Core initialization completed
[2025-09-01 07:11:27] [INFO] [API] API handlers initialized
[2025-09-01 07:11:27] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:11:27] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:11:27] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:11:27] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:11:27] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:11:27] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:11:27] [INFO] [CORE] Starting core initialization
[2025-09-01 07:11:27] [INFO] [CORE] Core initialization completed
[2025-09-01 07:11:32] [INFO] [API] API handlers initialized
[2025-09-01 07:11:33] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:11:33] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:11:33] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:11:33] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:11:33] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:11:33] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:11:33] [INFO] [CORE] Starting core initialization
[2025-09-01 07:11:33] [INFO] [ADMIN] Admin interface initialized
[2025-09-01 07:11:33] [INFO] [CORE] Admin functionality loaded
[2025-09-01 07:11:33] [INFO] [CORE] Core initialization completed
[2025-09-01 07:11:33] [INFO] [ADMIN] Settings initialized
[2025-09-01 07:11:42] [INFO] [API] API handlers initialized
[2025-09-01 07:11:42] [INFO] [API] API handlers initialized
[2025-09-01 07:11:42] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:11:42] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:11:42] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:11:42] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:11:42] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:11:42] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:11:42] [INFO] [CORE] Starting core initialization
[2025-09-01 07:11:42] [INFO] [ADMIN] Admin interface initialized
[2025-09-01 07:11:42] [INFO] [CORE] Admin functionality loaded
[2025-09-01 07:11:42] [INFO] [CORE] Core initialization completed
[2025-09-01 07:11:43] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:11:43] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:11:43] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:11:43] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:11:43] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:11:43] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:11:43] [INFO] [CORE] Starting core initialization
[2025-09-01 07:11:43] [INFO] [ADMIN] Admin interface initialized
[2025-09-01 07:11:43] [INFO] [CORE] Admin functionality loaded
[2025-09-01 07:11:43] [INFO] [CORE] Core initialization completed
[2025-09-01 07:11:43] [INFO] [ADMIN] Admin menu created
[2025-09-01 07:11:43] [INFO] [ADMIN] Settings initialized
[2025-09-01 07:11:44] [INFO] [ADMIN] Settings initialized
[2025-09-01 07:11:44] [INFO] [API] API handlers initialized
[2025-09-01 07:11:44] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:11:44] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:11:44] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:11:44] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:11:44] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:11:44] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:11:44] [INFO] [CORE] Starting core initialization
[2025-09-01 07:11:44] [INFO] [ADMIN] Admin interface initialized
[2025-09-01 07:11:44] [INFO] [CORE] Admin functionality loaded
[2025-09-01 07:11:44] [INFO] [CORE] Core initialization completed
[2025-09-01 07:11:45] [INFO] [ADMIN] Admin menu created
[2025-09-01 07:11:45] [INFO] [ADMIN] Settings initialized
[2025-09-01 07:11:46] [INFO] [API] API handlers initialized
[2025-09-01 07:11:47] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:11:47] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:11:47] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:11:47] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:11:47] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:11:47] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:11:47] [INFO] [CORE] Starting core initialization
[2025-09-01 07:11:47] [INFO] [CORE] Core initialization completed
[2025-09-01 07:11:48] [INFO] [API] API handlers initialized
[2025-09-01 07:11:48] [INFO] [API] API handlers initialized
[2025-09-01 07:11:48] [INFO] [API] API handlers initialized
[2025-09-01 07:11:48] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:11:48] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:11:48] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:11:48] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:11:48] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:11:48] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:11:48] [INFO] [CORE] Starting core initialization
[2025-09-01 07:11:48] [INFO] [CORE] Core initialization completed
[2025-09-01 07:11:48] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:11:48] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:11:48] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:11:48] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:11:48] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:11:48] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:11:48] [INFO] [CORE] Starting core initialization
[2025-09-01 07:11:48] [INFO] [CORE] Core initialization completed
[2025-09-01 07:11:48] [INFO] [API] API handlers initialized
[2025-09-01 07:11:49] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:11:49] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:11:49] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:11:49] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:11:49] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:11:49] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:11:49] [INFO] [CORE] Starting core initialization
[2025-09-01 07:11:49] [INFO] [CORE] Core initialization completed
[2025-09-01 07:11:49] [INFO] [API] API handlers initialized
[2025-09-01 07:11:49] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:11:49] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:11:49] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:11:49] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:11:49] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:11:49] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:11:49] [INFO] [CORE] Starting core initialization
[2025-09-01 07:11:49] [INFO] [CORE] Core initialization completed
[2025-09-01 07:11:49] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:11:49] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:11:49] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:11:49] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:11:49] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:11:49] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:11:49] [INFO] [CORE] Starting core initialization
[2025-09-01 07:11:49] [INFO] [CORE] Core initialization completed
[2025-09-01 07:11:51] [INFO] [API] API handlers initialized
[2025-09-01 07:11:51] [INFO] [API] API handlers initialized
[2025-09-01 07:11:51] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:11:51] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:11:51] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:11:51] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:11:51] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:11:51] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:11:51] [INFO] [CORE] Starting core initialization
[2025-09-01 07:11:51] [INFO] [CORE] Core initialization completed
[2025-09-01 07:11:51] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:11:51] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:11:51] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:11:51] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:11:51] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:11:51] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:11:51] [INFO] [CORE] Starting core initialization
[2025-09-01 07:11:51] [INFO] [CORE] Core initialization completed
[2025-09-01 07:11:52] [INFO] [API] API handlers initialized
[2025-09-01 07:11:52] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:11:52] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:11:52] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:11:52] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:11:52] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:11:52] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:11:52] [INFO] [CORE] Starting core initialization
[2025-09-01 07:11:52] [INFO] [ADMIN] Admin interface initialized
[2025-09-01 07:11:52] [INFO] [CORE] Admin functionality loaded
[2025-09-01 07:11:52] [INFO] [CORE] Core initialization completed
[2025-09-01 07:11:53] [INFO] [API] API handlers initialized
[2025-09-01 07:11:53] [INFO] [ADMIN] Settings initialized
[2025-09-01 07:11:53] [INFO] [API] API handlers initialized
[2025-09-01 07:11:53] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:11:53] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:11:53] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:11:53] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:11:53] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:11:53] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:11:53] [INFO] [CORE] Starting core initialization
[2025-09-01 07:11:53] [INFO] [ADMIN] Admin interface initialized
[2025-09-01 07:11:53] [INFO] [CORE] Admin functionality loaded
[2025-09-01 07:11:53] [INFO] [CORE] Core initialization completed
[2025-09-01 07:11:54] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:11:54] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:11:54] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:11:54] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:11:54] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:11:54] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:11:54] [INFO] [CORE] Starting core initialization
[2025-09-01 07:11:54] [INFO] [CORE] Core initialization completed
[2025-09-01 07:11:54] [INFO] [ADMIN] Settings initialized
[2025-09-01 07:11:55] [INFO] [API] API handlers initialized
[2025-09-01 07:11:55] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:11:55] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:11:55] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:11:55] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:11:55] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:11:55] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:11:55] [INFO] [CORE] Starting core initialization
[2025-09-01 07:11:55] [INFO] [CORE] Core initialization completed
[2025-09-01 07:11:57] [INFO] [API] API handlers initialized
[2025-09-01 07:11:57] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:11:57] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:11:57] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:11:57] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:11:57] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:11:57] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:11:57] [INFO] [CORE] Starting core initialization
[2025-09-01 07:11:57] [INFO] [CORE] Core initialization completed
[2025-09-01 07:12:02] [INFO] [API] API handlers initialized
[2025-09-01 07:12:03] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:12:03] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:12:03] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:12:03] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:12:03] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:12:03] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:12:03] [INFO] [CORE] Starting core initialization
[2025-09-01 07:12:03] [INFO] [ADMIN] Admin interface initialized
[2025-09-01 07:12:03] [INFO] [CORE] Admin functionality loaded
[2025-09-01 07:12:03] [INFO] [CORE] Core initialization completed
[2025-09-01 07:12:03] [INFO] [ADMIN] Settings initialized
[2025-09-01 07:12:03] [INFO] [API] API handlers initialized
[2025-09-01 07:12:04] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:12:04] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:12:04] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:12:04] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:12:04] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:12:04] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:12:04] [INFO] [CORE] Starting core initialization
[2025-09-01 07:12:04] [INFO] [ADMIN] Admin interface initialized
[2025-09-01 07:12:04] [INFO] [CORE] Admin functionality loaded
[2025-09-01 07:12:04] [INFO] [CORE] Core initialization completed
[2025-09-01 07:12:04] [INFO] [API] API handlers initialized
[2025-09-01 07:12:05] [INFO] [ADMIN] Settings initialized
[2025-09-01 07:12:05] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:12:05] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:12:05] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:12:05] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:12:05] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:12:05] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:12:05] [INFO] [CORE] Starting core initialization
[2025-09-01 07:12:05] [INFO] [CORE] Core initialization completed
[2025-09-01 07:12:06] [INFO] [MASTERSTUDY] Course 50007 status changed from draft to publish
[2025-09-01 07:12:06] [INFO] [MASTERSTUDY] Course published automatically: 50007
[2025-09-01 07:12:06] [INFO] [MASTERSTUDY] Attempting to sync course 50007 to VedMG database
[2025-09-01 07:12:06] [INFO] [MASTERSTUDY] Updated course in VedMG database: 50007
[2025-09-01 07:12:06] [INFO] [MASTERSTUDY] Successfully synced course 50007 to VedMG database
[2025-09-01 07:12:06] [INFO] [MASTERSTUDY] Creating classroom automatically for course 50007
[2025-09-01 07:12:06] [INFO] [API] Making API call
[2025-09-01 07:12:12] [INFO] [API] API handlers initialized
[2025-09-01 07:12:13] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:12:13] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:12:13] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:12:13] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:12:13] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:12:13] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:12:13] [INFO] [CORE] Starting core initialization
[2025-09-01 07:12:13] [INFO] [ADMIN] Admin interface initialized
[2025-09-01 07:12:13] [INFO] [CORE] Admin functionality loaded
[2025-09-01 07:12:13] [INFO] [CORE] Core initialization completed
[2025-09-01 07:12:13] [INFO] [API] API response received
[2025-09-01 07:12:13] [INFO] [API] API call successful
[2025-09-01 07:12:13] [INFO] [MASTERSTUDY] No calendar ID in classroom response, using fallback API for course 50007
[2025-09-01 07:12:13] [INFO] [API] Making API call
[2025-09-01 07:12:14] [INFO] [ADMIN] Settings initialized
[2025-09-01 07:12:16] [INFO] [API] API response received
[2025-09-01 07:12:16] [INFO] [API] API call successful
[2025-09-01 07:12:16] [INFO] [MASTERSTUDY] Fallback calendar API returned calendar ID: <EMAIL>
[2025-09-01 07:12:16] [INFO] [MASTERSTUDY] Classroom created automatically for course 50007: 801060685359 with calendar: <EMAIL>
[2025-09-01 07:12:16] [INFO] [MASTERSTUDY] Course save event: 50007 (update: yes)
[2025-09-01 07:12:16] [INFO] [MASTERSTUDY] Updated course in VedMG database: 50007
[2025-09-01 07:12:22] [INFO] [API] API handlers initialized
[2025-09-01 07:12:23] [INFO] [API] API handlers initialized
[2025-09-01 07:12:23] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:12:23] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:12:23] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:12:23] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:12:23] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:12:23] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:12:23] [INFO] [CORE] Starting core initialization
[2025-09-01 07:12:23] [INFO] [ADMIN] Admin interface initialized
[2025-09-01 07:12:23] [INFO] [CORE] Admin functionality loaded
[2025-09-01 07:12:23] [INFO] [CORE] Core initialization completed
[2025-09-01 07:12:23] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:12:23] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:12:23] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:12:23] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:12:23] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:12:23] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:12:23] [INFO] [CORE] Starting core initialization
[2025-09-01 07:12:23] [INFO] [CORE] Core initialization completed
[2025-09-01 07:12:24] [INFO] [API] API handlers initialized
[2025-09-01 07:12:24] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:12:24] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:12:24] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:12:24] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:12:24] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:12:24] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:12:24] [INFO] [CORE] Starting core initialization
[2025-09-01 07:12:24] [INFO] [CORE] Core initialization completed
[2025-09-01 07:12:24] [INFO] [API] API handlers initialized
[2025-09-01 07:12:25] [INFO] [ADMIN] Settings initialized
[2025-09-01 07:12:25] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:12:25] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:12:25] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:12:25] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:12:25] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:12:25] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:12:25] [INFO] [CORE] Starting core initialization
[2025-09-01 07:12:25] [INFO] [CORE] Core initialization completed
[2025-09-01 07:12:27] [INFO] [API] API handlers initialized
[2025-09-01 07:12:28] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:12:28] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:12:28] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:12:28] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:12:28] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:12:28] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:12:28] [INFO] [CORE] Starting core initialization
[2025-09-01 07:12:28] [INFO] [CORE] Core initialization completed
[2025-09-01 07:12:29] [INFO] [API] API handlers initialized
[2025-09-01 07:12:29] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:12:29] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:12:29] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:12:29] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:12:29] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:12:29] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:12:29] [INFO] [CORE] Starting core initialization
[2025-09-01 07:12:29] [INFO] [CORE] Core initialization completed
[2025-09-01 07:12:30] [INFO] [API] API handlers initialized
[2025-09-01 07:12:30] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:12:30] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:12:30] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:12:30] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:12:30] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:12:30] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:12:30] [INFO] [CORE] Starting core initialization
[2025-09-01 07:12:30] [INFO] [CORE] Core initialization completed
[2025-09-01 07:12:32] [INFO] [API] API handlers initialized
[2025-09-01 07:12:33] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:12:33] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:12:33] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:12:33] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:12:33] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:12:33] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:12:33] [INFO] [CORE] Starting core initialization
[2025-09-01 07:12:33] [INFO] [CORE] Core initialization completed
[2025-09-01 07:12:33] [INFO] [API] API handlers initialized
[2025-09-01 07:12:33] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:12:33] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:12:33] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:12:33] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:12:33] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:12:33] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:12:33] [INFO] [CORE] Starting core initialization
[2025-09-01 07:12:33] [INFO] [ADMIN] Admin interface initialized
[2025-09-01 07:12:33] [INFO] [CORE] Admin functionality loaded
[2025-09-01 07:12:33] [INFO] [CORE] Core initialization completed
[2025-09-01 07:12:34] [INFO] [ADMIN] Settings initialized
[2025-09-01 07:12:35] [INFO] [API] API handlers initialized
[2025-09-01 07:12:36] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:12:36] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:12:36] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:12:36] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:12:36] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:12:36] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:12:36] [INFO] [CORE] Starting core initialization
[2025-09-01 07:12:36] [INFO] [ADMIN] Admin interface initialized
[2025-09-01 07:12:36] [INFO] [CORE] Admin functionality loaded
[2025-09-01 07:12:36] [INFO] [CORE] Core initialization completed
[2025-09-01 07:12:36] [INFO] [ADMIN] Admin menu created
[2025-09-01 07:12:37] [INFO] [ADMIN] Settings initialized
[2025-09-01 07:12:38] [INFO] [API] API handlers initialized
[2025-09-01 07:12:38] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:12:38] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:12:38] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:12:38] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:12:38] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:12:38] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:12:38] [INFO] [CORE] Starting core initialization
[2025-09-01 07:12:38] [INFO] [ADMIN] Admin interface initialized
[2025-09-01 07:12:38] [INFO] [CORE] Admin functionality loaded
[2025-09-01 07:12:38] [INFO] [CORE] Core initialization completed
[2025-09-01 07:12:39] [INFO] [ADMIN] Settings initialized
[2025-09-01 07:12:49] [INFO] [API] API handlers initialized
[2025-09-01 07:12:50] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:12:50] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:12:50] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:12:50] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:12:50] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:12:50] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:12:50] [INFO] [CORE] Starting core initialization
[2025-09-01 07:12:50] [INFO] [ADMIN] Admin interface initialized
[2025-09-01 07:12:50] [INFO] [CORE] Admin functionality loaded
[2025-09-01 07:12:50] [INFO] [CORE] Core initialization completed
[2025-09-01 07:12:50] [INFO] [ADMIN] Admin menu created
[2025-09-01 07:12:50] [INFO] [ADMIN] Settings initialized
[2025-09-01 07:12:51] [INFO] [API] API handlers initialized
[2025-09-01 07:12:52] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:12:52] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:12:52] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:12:52] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:12:52] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:12:52] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:12:52] [INFO] [CORE] Starting core initialization
[2025-09-01 07:12:52] [INFO] [ADMIN] Admin interface initialized
[2025-09-01 07:12:52] [INFO] [CORE] Admin functionality loaded
[2025-09-01 07:12:52] [INFO] [CORE] Core initialization completed
[2025-09-01 07:12:52] [INFO] [ADMIN] Admin menu created
[2025-09-01 07:12:53] [INFO] [ADMIN] Settings initialized
[2025-09-01 07:12:59] [INFO] [API] API handlers initialized
[2025-09-01 07:13:00] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:13:00] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:13:00] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:13:00] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:13:00] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:13:00] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:13:00] [INFO] [CORE] Starting core initialization
[2025-09-01 07:13:00] [INFO] [ADMIN] Admin interface initialized
[2025-09-01 07:13:00] [INFO] [CORE] Admin functionality loaded
[2025-09-01 07:13:00] [INFO] [CORE] Core initialization completed
[2025-09-01 07:13:00] [INFO] [ADMIN] Admin menu created
[2025-09-01 07:13:01] [INFO] [ADMIN] Settings initialized
[2025-09-01 07:13:01] [INFO] [ADMIN] Admin assets enqueued for page: courses
[2025-09-01 07:13:01] [ACTION] [ADMIN] Admin action: Accessed courses page | User: localadmin (ID: 27)
[2025-09-01 07:13:01] [ACTION] [ADMIN] Admin action: Viewed courses management page | User: localadmin (ID: 27)
[2025-09-01 07:13:01] [DATABASE] [DB] Database SELECT on table: vedmg_courses | Details: Retrieved 10 courses (page 1 of 6)
[2025-09-01 07:13:01] [DATABASE] [DB] Database SELECT on table: classroom_options | Details: Retrieved 26 classroom options from database
[2025-09-01 07:13:32] [INFO] [API] API handlers initialized
[2025-09-01 07:13:34] [INFO] [API] API handlers initialized
[2025-09-01 07:13:51] [INFO] [API] API handlers initialized
[2025-09-01 07:13:57] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:13:57] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:13:57] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:13:57] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:13:57] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:13:57] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:13:57] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:13:57] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:13:57] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:13:57] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:13:57] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:13:57] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:13:57] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:13:57] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:13:57] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:13:57] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:13:57] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:13:57] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:13:57] [INFO] [CORE] Starting core initialization
[2025-09-01 07:13:57] [INFO] [CORE] Starting core initialization
[2025-09-01 07:13:57] [INFO] [CORE] Starting core initialization
[2025-09-01 07:13:57] [INFO] [ADMIN] Admin interface initialized
[2025-09-01 07:13:57] [INFO] [CORE] Admin functionality loaded
[2025-09-01 07:13:57] [INFO] [ADMIN] Admin interface initialized
[2025-09-01 07:13:57] [INFO] [CORE] Core initialization completed
[2025-09-01 07:13:57] [INFO] [CORE] Admin functionality loaded
[2025-09-01 07:13:57] [INFO] [CORE] Core initialization completed
[2025-09-01 07:13:57] [INFO] [ADMIN] Admin interface initialized
[2025-09-01 07:13:57] [INFO] [CORE] Admin functionality loaded
[2025-09-01 07:13:57] [INFO] [CORE] Core initialization completed
[2025-09-01 07:14:01] [INFO] [API] API handlers initialized
[2025-09-01 07:14:02] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:14:02] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:14:02] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:14:02] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:14:02] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:14:02] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:14:02] [INFO] [CORE] Starting core initialization
[2025-09-01 07:14:02] [INFO] [ADMIN] Admin interface initialized
[2025-09-01 07:14:02] [INFO] [CORE] Admin functionality loaded
[2025-09-01 07:14:02] [INFO] [CORE] Core initialization completed
[2025-09-01 07:14:25] [INFO] [API] API handlers initialized
[2025-09-01 07:14:25] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:14:25] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:14:25] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:14:25] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:14:25] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:14:25] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:14:25] [INFO] [CORE] Starting core initialization
[2025-09-01 07:14:25] [INFO] [CORE] Core initialization completed
[2025-09-01 07:14:31] [INFO] [API] API handlers initialized
[2025-09-01 07:14:32] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:14:32] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:14:32] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:14:32] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:14:32] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:14:32] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:14:32] [INFO] [CORE] Starting core initialization
[2025-09-01 07:14:32] [INFO] [CORE] Core initialization completed
[2025-09-01 07:14:36] [INFO] [API] API handlers initialized
[2025-09-01 07:14:37] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:14:37] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:14:37] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:14:37] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:14:37] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:14:37] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:14:37] [INFO] [CORE] Starting core initialization
[2025-09-01 07:14:37] [INFO] [CORE] Core initialization completed
[2025-09-01 07:14:39] [INFO] [API] API handlers initialized
[2025-09-01 07:14:40] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:14:40] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:14:40] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:14:40] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:14:40] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:14:40] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:14:40] [INFO] [CORE] Starting core initialization
[2025-09-01 07:14:40] [INFO] [CORE] Core initialization completed
[2025-09-01 07:14:40] [INFO] [ADMIN] Admin menu created
[2025-09-01 07:14:40] [INFO] [ADMIN] Admin menu created
[2025-09-01 07:14:40] [INFO] [ADMIN] Admin menu created
[2025-09-01 07:14:41] [INFO] [ADMIN] Settings initialized
[2025-09-01 07:14:44] [INFO] [ADMIN] Settings initialized
[2025-09-01 07:14:44] [INFO] [ADMIN] Settings initialized
[2025-09-01 07:14:44] [INFO] [ADMIN] Settings initialized
[2025-09-01 07:14:44] [INFO] [ADMIN] Admin assets enqueued for page: dashboard
[2025-09-01 07:14:44] [INFO] [ADMIN] Admin assets enqueued for page: dashboard
[2025-09-01 07:14:44] [INFO] [ADMIN] Admin assets enqueued for page: courses
[2025-09-01 07:14:46] [INFO] [API] API handlers initialized
[2025-09-01 07:14:46] [ACTION] [ADMIN] Admin action: Accessed instructor sync page | User: localadmin (ID: 27)
[2025-09-01 07:14:46] [ACTION] [ADMIN] Admin action: Accessed instructor sync page | User: localadmin (ID: 27)
[2025-09-01 07:14:46] [ACTION] [ADMIN] Admin action: Accessed courses page | User: localadmin (ID: 27)
[2025-09-01 07:14:46] [ACTION] [ADMIN] Admin action: Viewed courses management page | User: localadmin (ID: 27)
[2025-09-01 07:14:46] [DATABASE] [DB] Database SELECT on table: vedmg_courses | Details: Retrieved 10 courses (page 1 of 6)
[2025-09-01 07:14:46] [DATABASE] [DB] Database SELECT on table: classroom_options | Details: Retrieved 26 classroom options from database
[2025-09-01 07:14:46] [ACTION] [ADMIN] Admin action: Viewed instructor sync page | User: localadmin (ID: 27)
[2025-09-01 07:14:46] [ACTION] [ADMIN] Admin action: Viewed instructor sync page | User: localadmin (ID: 27)
[2025-09-01 07:14:46] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:14:46] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:14:46] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:14:46] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:14:46] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:14:46] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:14:46] [INFO] [CORE] Starting core initialization
[2025-09-01 07:14:46] [INFO] [CORE] Core initialization completed
[2025-09-01 07:14:48] [INFO] [API] API handlers initialized
[2025-09-01 07:14:48] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:14:48] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:14:48] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:14:48] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:14:48] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:14:48] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:14:48] [INFO] [CORE] Starting core initialization
[2025-09-01 07:14:48] [INFO] [ADMIN] Admin interface initialized
[2025-09-01 07:14:48] [INFO] [CORE] Admin functionality loaded
[2025-09-01 07:14:48] [INFO] [CORE] Core initialization completed
[2025-09-01 07:14:49] [INFO] [ADMIN] Admin menu created
[2025-09-01 07:14:49] [INFO] [ADMIN] Settings initialized
[2025-09-01 07:14:54] [INFO] [API] API handlers initialized
[2025-09-01 07:14:55] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:14:55] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:14:55] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:14:55] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:14:55] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:14:55] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:14:55] [INFO] [CORE] Starting core initialization
[2025-09-01 07:14:55] [INFO] [ADMIN] Admin interface initialized
[2025-09-01 07:14:55] [INFO] [CORE] Admin functionality loaded
[2025-09-01 07:14:55] [INFO] [CORE] Core initialization completed
[2025-09-01 07:14:56] [INFO] [ADMIN] Settings initialized
[2025-09-01 07:15:00] [INFO] [API] API handlers initialized
[2025-09-01 07:15:01] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:15:01] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:15:01] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:15:01] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:15:01] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:15:01] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:15:01] [INFO] [CORE] Starting core initialization
[2025-09-01 07:15:01] [INFO] [ADMIN] Admin interface initialized
[2025-09-01 07:15:01] [INFO] [CORE] Admin functionality loaded
[2025-09-01 07:15:01] [INFO] [CORE] Core initialization completed
[2025-09-01 07:15:01] [INFO] [ADMIN] Admin menu created
[2025-09-01 07:15:01] [INFO] [ADMIN] Settings initialized
[2025-09-01 07:15:01] [INFO] [ADMIN] Admin assets enqueued for page: dashboard
[2025-09-01 07:15:02] [ACTION] [ADMIN] Admin action: Accessed instructor sync page | User: localadmin (ID: 27)
[2025-09-01 07:15:02] [ACTION] [ADMIN] Admin action: Viewed instructor sync page | User: localadmin (ID: 27)
[2025-09-01 07:15:02] [INFO] [API] API handlers initialized
[2025-09-01 07:15:03] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:15:03] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:15:03] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:15:03] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:15:03] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:15:03] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:15:03] [INFO] [CORE] Starting core initialization
[2025-09-01 07:15:03] [INFO] [ADMIN] Admin interface initialized
[2025-09-01 07:15:03] [INFO] [CORE] Admin functionality loaded
[2025-09-01 07:15:03] [INFO] [CORE] Core initialization completed
[2025-09-01 07:15:04] [INFO] [ADMIN] Settings initialized
[2025-09-01 07:15:04] [INFO] [API] Getting all instructors for dropdown
[2025-09-01 07:15:04] [DATABASE] [DB] Database SELECT on table: instructors_paginated | Details: Retrieved 1000 instructors (page 1 of 1), total: 22
[2025-09-01 07:15:04] [INFO] [API] Found 22 instructors
[2025-09-01 07:15:07] [INFO] [API] API handlers initialized
[2025-09-01 07:15:07] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:15:07] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:15:07] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:15:07] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:15:07] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:15:07] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:15:07] [INFO] [CORE] Starting core initialization
[2025-09-01 07:15:07] [INFO] [ADMIN] Admin interface initialized
[2025-09-01 07:15:07] [INFO] [CORE] Admin functionality loaded
[2025-09-01 07:15:07] [INFO] [CORE] Core initialization completed
[2025-09-01 07:15:08] [INFO] [ADMIN] Admin menu created
[2025-09-01 07:15:08] [INFO] [ADMIN] Settings initialized
[2025-09-01 07:15:08] [INFO] [ADMIN] Admin assets enqueued for page: dashboard
[2025-09-01 07:15:09] [ACTION] [ADMIN] Admin action: Accessed instructor sync page | User: localadmin (ID: 27)
[2025-09-01 07:15:09] [ACTION] [ADMIN] Admin action: Viewed instructor sync page | User: localadmin (ID: 27)
[2025-09-01 07:15:09] [INFO] [API] API handlers initialized
[2025-09-01 07:15:10] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:15:10] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:15:10] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:15:10] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:15:10] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:15:10] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:15:10] [INFO] [CORE] Starting core initialization
[2025-09-01 07:15:10] [INFO] [ADMIN] Admin interface initialized
[2025-09-01 07:15:10] [INFO] [CORE] Admin functionality loaded
[2025-09-01 07:15:10] [INFO] [CORE] Core initialization completed
[2025-09-01 07:15:11] [INFO] [ADMIN] Settings initialized
[2025-09-01 07:15:11] [INFO] [API] Getting all instructors for dropdown
[2025-09-01 07:15:11] [DATABASE] [DB] Database SELECT on table: instructors_paginated | Details: Retrieved 1000 instructors (page 1 of 1), total: 22
[2025-09-01 07:15:11] [INFO] [API] Found 22 instructors
[2025-09-01 07:15:28] [INFO] [API] API handlers initialized
[2025-09-01 07:15:29] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:15:29] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:15:29] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:15:29] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:15:29] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:15:29] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:15:29] [INFO] [CORE] Starting core initialization
[2025-09-01 07:15:29] [INFO] [ADMIN] Admin interface initialized
[2025-09-01 07:15:29] [INFO] [CORE] Admin functionality loaded
[2025-09-01 07:15:29] [INFO] [CORE] Core initialization completed
[2025-09-01 07:15:30] [INFO] [API] API handlers initialized
[2025-09-01 07:15:30] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:15:30] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:15:30] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:15:30] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:15:30] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:15:30] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:15:30] [INFO] [CORE] Starting core initialization
[2025-09-01 07:15:30] [INFO] [CORE] Core initialization completed
[2025-09-01 07:15:30] [INFO] [ADMIN] Settings initialized
[2025-09-01 07:15:30] [INFO] [API] Starting Google Classroom sync for: <EMAIL> (Selected: Kushagra Mishra, ID: 24)
[2025-09-01 07:15:30] [INFO] [API] Found 36 courses for instructor ID: 24
[2025-09-01 07:15:30] [INFO] [API] Fetching courses from: https://gclassroom-839391304260.us-central1.run.app/list_courses for: <EMAIL>
[2025-09-01 07:15:32] [INFO] [API] Successfully fetched 8 courses from Google Classroom
[2025-09-01 07:15:32] [INFO] [API] Starting hybrid matching: Phase 1 - Google Classroom ID matching
[2025-09-01 07:15:32] [INFO] [API] Calendar ID updated from Google Classroom API (ID match)
[2025-09-01 07:15:32] [INFO] [API] Course matched by ID: tata motors → tata motors (ID: 801060685359)
[2025-09-01 07:15:32] [INFO] [API] Course matched by ID: flow testing → flow testing (ID: 799768958460)
[2025-09-01 07:15:32] [INFO] [API] Course matched by ID: re test → re test (ID: 800162928923)
[2025-09-01 07:15:32] [INFO] [API] Calendar ID updated from Google Classroom API (ID match)
[2025-09-01 07:15:32] [INFO] [API] Course matched by ID: live presentation → live presentation (ID: 800202844550)
[2025-09-01 07:15:32] [INFO] [API] Calendar ID updated from Google Classroom API (ID match)
[2025-09-01 07:15:32] [INFO] [API] Course matched by ID: entire flow test → entire flow test (ID: 800579699721)
[2025-09-01 07:15:32] [INFO] [API] Calendar ID updated from Google Classroom API (ID match)
[2025-09-01 07:15:32] [INFO] [API] Course matched by ID: final flow testing → final flow testing (ID: 780262248270)
[2025-09-01 07:15:32] [INFO] [API] Calendar ID updated from Google Classroom API (ID match)
[2025-09-01 07:15:32] [INFO] [API] Course matched by ID: video recording → video recording (ID: 800837934243)
[2025-09-01 07:15:32] [INFO] [API] Phase 1 complete: 7 exact ID matches. Starting Phase 2 - Name matching
[2025-09-01 07:15:32] [INFO] [API] Unmatched Google Classroom course (not creating): Overview of VedMG Academy
[2025-09-01 07:15:32] [INFO] [API] Google Classroom sync completed
[2025-09-01 07:15:38] [INFO] [API] API handlers initialized
[2025-09-01 07:15:39] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:15:39] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:15:39] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:15:39] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:15:39] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:15:39] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:15:39] [INFO] [CORE] Starting core initialization
[2025-09-01 07:15:39] [INFO] [ADMIN] Admin interface initialized
[2025-09-01 07:15:39] [INFO] [CORE] Admin functionality loaded
[2025-09-01 07:15:39] [INFO] [CORE] Core initialization completed
[2025-09-01 07:15:39] [INFO] [ADMIN] Admin menu created
[2025-09-01 07:15:39] [INFO] [ADMIN] Settings initialized
[2025-09-01 07:15:39] [INFO] [ADMIN] Admin assets enqueued for page: dashboard
[2025-09-01 07:15:40] [ACTION] [ADMIN] Admin action: Accessed instructor sync page | User: localadmin (ID: 27)
[2025-09-01 07:15:40] [ACTION] [ADMIN] Admin action: Viewed instructor sync page | User: localadmin (ID: 27)
[2025-09-01 07:15:40] [INFO] [API] API handlers initialized
[2025-09-01 07:15:41] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:15:41] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:15:41] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:15:41] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:15:41] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:15:41] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:15:41] [INFO] [CORE] Starting core initialization
[2025-09-01 07:15:41] [INFO] [ADMIN] Admin interface initialized
[2025-09-01 07:15:41] [INFO] [CORE] Admin functionality loaded
[2025-09-01 07:15:41] [INFO] [CORE] Core initialization completed
[2025-09-01 07:15:42] [INFO] [ADMIN] Settings initialized
[2025-09-01 07:15:42] [INFO] [API] Getting all instructors for dropdown
[2025-09-01 07:15:42] [DATABASE] [DB] Database SELECT on table: instructors_paginated | Details: Retrieved 1000 instructors (page 1 of 1), total: 22
[2025-09-01 07:15:42] [INFO] [API] Found 22 instructors
[2025-09-01 07:15:51] [INFO] [API] API handlers initialized
[2025-09-01 07:15:51] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:15:51] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:15:51] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:15:51] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:15:51] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:15:51] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:15:51] [INFO] [CORE] Starting core initialization
[2025-09-01 07:15:51] [INFO] [ADMIN] Admin interface initialized
[2025-09-01 07:15:51] [INFO] [CORE] Admin functionality loaded
[2025-09-01 07:15:51] [INFO] [CORE] Core initialization completed
[2025-09-01 07:15:52] [INFO] [ADMIN] Admin menu created
[2025-09-01 07:15:52] [INFO] [ADMIN] Settings initialized
[2025-09-01 07:15:53] [INFO] [ADMIN] Admin assets enqueued for page: courses
[2025-09-01 07:15:53] [ACTION] [ADMIN] Admin action: Accessed courses page | User: localadmin (ID: 27)
[2025-09-01 07:15:53] [ACTION] [ADMIN] Admin action: Viewed courses management page | User: localadmin (ID: 27)
[2025-09-01 07:15:53] [DATABASE] [DB] Database SELECT on table: vedmg_courses | Details: Retrieved 10 courses (page 1 of 6)
[2025-09-01 07:15:53] [DATABASE] [DB] Database SELECT on table: classroom_options | Details: Retrieved 27 classroom options from database
[2025-09-01 07:16:10] [INFO] [API] API handlers initialized
[2025-09-01 07:16:10] [INFO] [API] API handlers initialized
[2025-09-01 07:16:11] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:16:11] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:16:11] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:16:11] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:16:11] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:16:11] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:16:11] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:16:11] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:16:11] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:16:11] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:16:11] [INFO] [CORE] Starting core initialization
[2025-09-01 07:16:11] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:16:11] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:16:11] [INFO] [CORE] Starting core initialization
[2025-09-01 07:16:11] [INFO] [ADMIN] Admin interface initialized
[2025-09-01 07:16:11] [INFO] [CORE] Admin functionality loaded
[2025-09-01 07:16:11] [INFO] [CORE] Core initialization completed
[2025-09-01 07:16:11] [INFO] [ADMIN] Admin interface initialized
[2025-09-01 07:16:11] [INFO] [CORE] Admin functionality loaded
[2025-09-01 07:16:11] [INFO] [CORE] Core initialization completed
[2025-09-01 07:16:12] [INFO] [ADMIN] Settings initialized
[2025-09-01 07:16:12] [INFO] [ADMIN] Settings initialized
[2025-09-01 07:16:12] [INFO] [API] Getting course data
[2025-09-01 07:16:12] [INFO] [DATABASE] Retrieved course 68: Found
[2025-09-01 07:16:12] [INFO] [API] Course data retrieved successfully
[2025-09-01 07:16:12] [ACTION] [ADMIN] Admin action: Found course creator: Kushagra Mishra (ID: 24) for course 68 | User: localadmin (ID: 27)
[2025-09-01 07:16:12] [DATABASE] [DB] Database SELECT on table: instructors_paginated | Details: Retrieved 1000 instructors (page 1 of 1), total: 22
[2025-09-01 07:16:12] [ACTION] [ADMIN] Admin action: Retrieved course creator and 22 instructors for course assignment | User: localadmin (ID: 27)
[2025-09-01 07:16:24] [INFO] [API] API handlers initialized
[2025-09-01 07:16:25] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:16:25] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:16:25] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:16:25] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:16:25] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:16:25] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:16:25] [INFO] [CORE] Starting core initialization
[2025-09-01 07:16:25] [INFO] [ADMIN] Admin interface initialized
[2025-09-01 07:16:25] [INFO] [CORE] Admin functionality loaded
[2025-09-01 07:16:25] [INFO] [CORE] Core initialization completed
[2025-09-01 07:16:25] [INFO] [ADMIN] Settings initialized
[2025-09-01 07:16:25] [INFO] [API] Saving meeting link for course
[2025-09-01 07:16:25] [INFO] [API] Meeting link saved successfully
[2025-09-01 07:16:28] [INFO] [API] API handlers initialized
[2025-09-01 07:16:28] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:16:28] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:16:28] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:16:28] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:16:28] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:16:28] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:16:28] [INFO] [CORE] Starting core initialization
[2025-09-01 07:16:28] [INFO] [ADMIN] Admin interface initialized
[2025-09-01 07:16:28] [INFO] [CORE] Admin functionality loaded
[2025-09-01 07:16:28] [INFO] [CORE] Core initialization completed
[2025-09-01 07:16:29] [INFO] [API] API handlers initialized
[2025-09-01 07:16:29] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:16:29] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:16:29] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:16:29] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:16:29] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:16:29] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:16:29] [INFO] [CORE] Starting core initialization
[2025-09-01 07:16:29] [INFO] [CORE] Core initialization completed
[2025-09-01 07:16:30] [INFO] [ADMIN] Admin menu created
[2025-09-01 07:16:30] [INFO] [ADMIN] Settings initialized
[2025-09-01 07:16:30] [INFO] [ADMIN] Admin assets enqueued for page: courses
[2025-09-01 07:16:30] [ACTION] [ADMIN] Admin action: Accessed courses page | User: localadmin (ID: 27)
[2025-09-01 07:16:30] [ACTION] [ADMIN] Admin action: Viewed courses management page | User: localadmin (ID: 27)
[2025-09-01 07:16:30] [DATABASE] [DB] Database SELECT on table: vedmg_courses | Details: Retrieved 10 courses (page 1 of 6)
[2025-09-01 07:16:30] [DATABASE] [DB] Database SELECT on table: classroom_options | Details: Retrieved 27 classroom options from database
[2025-09-01 07:16:32] [INFO] [API] API handlers initialized
[2025-09-01 07:16:32] [INFO] [API] API handlers initialized
[2025-09-01 07:16:32] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:16:32] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:16:32] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:16:32] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:16:32] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:16:32] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:16:32] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:16:32] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:16:32] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:16:32] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:16:32] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:16:32] [INFO] [CORE] Starting core initialization
[2025-09-01 07:16:32] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:16:32] [INFO] [CORE] Starting core initialization
[2025-09-01 07:16:32] [INFO] [ADMIN] Admin interface initialized
[2025-09-01 07:16:32] [INFO] [ADMIN] Admin interface initialized
[2025-09-01 07:16:32] [INFO] [CORE] Admin functionality loaded
[2025-09-01 07:16:32] [INFO] [CORE] Admin functionality loaded
[2025-09-01 07:16:32] [INFO] [CORE] Core initialization completed
[2025-09-01 07:16:32] [INFO] [CORE] Core initialization completed
[2025-09-01 07:16:33] [INFO] [ADMIN] Settings initialized
[2025-09-01 07:16:33] [INFO] [ADMIN] Settings initialized
[2025-09-01 07:16:33] [INFO] [API] Getting course data
[2025-09-01 07:16:33] [INFO] [API] Course data retrieved successfully
[2025-09-01 07:16:33] [INFO] [DATABASE] Retrieved course 68: Found
[2025-09-01 07:16:33] [ACTION] [ADMIN] Admin action: Found course creator: Kushagra Mishra (ID: 24) for course 68 | User: localadmin (ID: 27)
[2025-09-01 07:16:33] [DATABASE] [DB] Database SELECT on table: instructors_paginated | Details: Retrieved 1000 instructors (page 1 of 1), total: 22
[2025-09-01 07:16:33] [ACTION] [ADMIN] Admin action: Retrieved course creator and 22 instructors for course assignment | User: localadmin (ID: 27)
[2025-09-01 07:17:02] [INFO] [API] API handlers initialized
[2025-09-01 07:17:03] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:17:03] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:17:03] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:17:03] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:17:03] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:17:03] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:17:03] [INFO] [CORE] Starting core initialization
[2025-09-01 07:17:03] [INFO] [CORE] Core initialization completed
[2025-09-01 07:17:03] [INFO] [API] API handlers initialized
[2025-09-01 07:17:04] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:17:04] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:17:04] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:17:04] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:17:04] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:17:04] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:17:04] [INFO] [CORE] Starting core initialization
[2025-09-01 07:17:04] [INFO] [CORE] Core initialization completed
[2025-09-01 07:17:31] [INFO] [API] API handlers initialized
[2025-09-01 07:17:32] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:17:32] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:17:32] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:17:32] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:17:32] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:17:32] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:17:32] [INFO] [CORE] Starting core initialization
[2025-09-01 07:17:32] [INFO] [ADMIN] Admin interface initialized
[2025-09-01 07:17:32] [INFO] [CORE] Admin functionality loaded
[2025-09-01 07:17:32] [INFO] [CORE] Core initialization completed
[2025-09-01 07:17:34] [INFO] [API] API handlers initialized
[2025-09-01 07:17:34] [INFO] [API] API handlers initialized
[2025-09-01 07:17:34] [INFO] [ADMIN] Settings initialized
[2025-09-01 07:17:35] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:17:35] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:17:35] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:17:35] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:17:35] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:17:35] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:17:35] [INFO] [CORE] Starting core initialization
[2025-09-01 07:17:35] [INFO] [CORE] Core initialization completed
[2025-09-01 07:17:35] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:17:35] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:17:35] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:17:35] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:17:35] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:17:35] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:17:35] [INFO] [CORE] Starting core initialization
[2025-09-01 07:17:35] [INFO] [ADMIN] Admin interface initialized
[2025-09-01 07:17:35] [INFO] [CORE] Admin functionality loaded
[2025-09-01 07:17:35] [INFO] [CORE] Core initialization completed
[2025-09-01 07:17:39] [INFO] [ADMIN] Settings initialized
[2025-09-01 07:17:41] [INFO] [API] API handlers initialized
[2025-09-01 07:17:43] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:17:43] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:17:43] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:17:43] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:17:43] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:17:43] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:17:43] [INFO] [CORE] Starting core initialization
[2025-09-01 07:17:43] [INFO] [CORE] Core initialization completed
[2025-09-01 07:17:51] [INFO] [API] API handlers initialized
[2025-09-01 07:17:51] [INFO] [API] API handlers initialized
[2025-09-01 07:17:52] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:17:52] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:17:52] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:17:52] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:17:52] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:17:52] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:17:52] [INFO] [CORE] Starting core initialization
[2025-09-01 07:17:52] [INFO] [CORE] Core initialization completed
[2025-09-01 07:17:52] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:17:52] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:17:52] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:17:52] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:17:52] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:17:52] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:17:52] [INFO] [CORE] Starting core initialization
[2025-09-01 07:17:52] [INFO] [ADMIN] Admin interface initialized
[2025-09-01 07:17:52] [INFO] [CORE] Admin functionality loaded
[2025-09-01 07:17:52] [INFO] [CORE] Core initialization completed
[2025-09-01 07:17:55] [INFO] [ADMIN] Settings initialized
[2025-09-01 07:18:03] [INFO] [API] API handlers initialized
[2025-09-01 07:18:03] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:18:03] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:18:03] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:18:03] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:18:03] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:18:03] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:18:03] [INFO] [CORE] Starting core initialization
[2025-09-01 07:18:03] [INFO] [CORE] Core initialization completed
[2025-09-01 07:18:10] [INFO] [API] API handlers initialized
[2025-09-01 07:18:11] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:18:11] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:18:11] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:18:11] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:18:11] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:18:11] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:18:11] [INFO] [CORE] Starting core initialization
[2025-09-01 07:18:11] [INFO] [CORE] Core initialization completed
[2025-09-01 07:18:15] [INFO] [API] API handlers initialized
[2025-09-01 07:18:15] [INFO] [API] API handlers initialized
[2025-09-01 07:18:15] [INFO] [API] API handlers initialized
[2025-09-01 07:18:15] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:18:15] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:18:15] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:18:15] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:18:15] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:18:15] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:18:15] [INFO] [CORE] Starting core initialization
[2025-09-01 07:18:15] [INFO] [CORE] Core initialization completed
[2025-09-01 07:18:16] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:18:16] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:18:16] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:18:16] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:18:16] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:18:16] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:18:16] [INFO] [CORE] Starting core initialization
[2025-09-01 07:18:16] [INFO] [ADMIN] Admin interface initialized
[2025-09-01 07:18:16] [INFO] [CORE] Admin functionality loaded
[2025-09-01 07:18:16] [INFO] [CORE] Core initialization completed
[2025-09-01 07:18:16] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:18:16] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:18:16] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:18:16] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:18:16] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:18:16] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:18:16] [INFO] [CORE] Starting core initialization
[2025-09-01 07:18:16] [INFO] [ADMIN] Admin interface initialized
[2025-09-01 07:18:16] [INFO] [CORE] Admin functionality loaded
[2025-09-01 07:18:16] [INFO] [CORE] Core initialization completed
[2025-09-01 07:18:17] [INFO] [ADMIN] Settings initialized
[2025-09-01 07:18:17] [INFO] [ADMIN] Settings initialized
[2025-09-01 07:18:19] [INFO] [API] API handlers initialized
[2025-09-01 07:18:19] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:18:19] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:18:19] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:18:19] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:18:19] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:18:19] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:18:19] [INFO] [CORE] Starting core initialization
[2025-09-01 07:18:19] [INFO] [ADMIN] Admin interface initialized
[2025-09-01 07:18:19] [INFO] [CORE] Admin functionality loaded
[2025-09-01 07:18:19] [INFO] [CORE] Core initialization completed
[2025-09-01 07:18:20] [INFO] [ADMIN] Settings initialized
[2025-09-01 07:18:20] [INFO] [API] API handlers initialized
[2025-09-01 07:18:20] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:18:20] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:18:20] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:18:20] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:18:20] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:18:20] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:18:20] [INFO] [CORE] Starting core initialization
[2025-09-01 07:18:20] [INFO] [ADMIN] Admin interface initialized
[2025-09-01 07:18:20] [INFO] [CORE] Admin functionality loaded
[2025-09-01 07:18:20] [INFO] [CORE] Core initialization completed
[2025-09-01 07:18:21] [INFO] [ADMIN] Settings initialized
[2025-09-01 07:18:21] [INFO] [API] API handlers initialized
[2025-09-01 07:18:21] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:18:21] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:18:21] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:18:21] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:18:21] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:18:21] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:18:21] [INFO] [CORE] Starting core initialization
[2025-09-01 07:18:21] [INFO] [CORE] Core initialization completed
[2025-09-01 07:18:26] [INFO] [API] API handlers initialized
[2025-09-01 07:18:26] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:18:26] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:18:26] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:18:26] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:18:26] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:18:26] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:18:26] [INFO] [CORE] Starting core initialization
[2025-09-01 07:18:26] [INFO] [CORE] Core initialization completed
[2025-09-01 07:18:27] [INFO] [API] API handlers initialized
[2025-09-01 07:18:27] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:18:27] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:18:27] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:18:27] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:18:27] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:18:27] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:18:27] [INFO] [CORE] Starting core initialization
[2025-09-01 07:18:27] [INFO] [CORE] Core initialization completed
[2025-09-01 07:18:29] [INFO] [API] API handlers initialized
[2025-09-01 07:18:30] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:18:30] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:18:30] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:18:30] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:18:30] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:18:30] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:18:30] [INFO] [CORE] Starting core initialization
[2025-09-01 07:18:30] [INFO] [CORE] Core initialization completed
[2025-09-01 07:18:54] [INFO] [API] API handlers initialized
[2025-09-01 07:18:55] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:18:55] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:18:55] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:18:55] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:18:55] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:18:55] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:18:55] [INFO] [CORE] Starting core initialization
[2025-09-01 07:18:55] [INFO] [CORE] Core initialization completed
[2025-09-01 07:18:58] [INFO] [API] API handlers initialized
[2025-09-01 07:18:58] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:18:58] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:18:58] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:18:58] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:18:58] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:18:58] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:18:58] [INFO] [CORE] Starting core initialization
[2025-09-01 07:18:58] [INFO] [CORE] Core initialization completed
[2025-09-01 07:19:03] [INFO] [API] API handlers initialized
[2025-09-01 07:19:04] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:19:04] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:19:04] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:19:04] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:19:04] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:19:04] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:19:04] [INFO] [CORE] Starting core initialization
[2025-09-01 07:19:04] [INFO] [CORE] Core initialization completed
[2025-09-01 07:19:05] [INFO] [WOOCOMMERCE] Processing COD order: 50011
[2025-09-01 07:19:05] [INFO] [WOOCOMMERCE] Checking Vedmg-woo-LMS mapping for product ID: 50008
[2025-09-01 07:19:05] [INFO] [WOOCOMMERCE] Found Vedmg-woo-LMS table: wp_linking_table
[2025-09-01 07:19:05] [INFO] [WOOCOMMERCE] Querying Vedmg-woo-LMS: SELECT course_id FROM wp_linking_table WHERE product_id = 50008 AND type = 'course' LIMIT 1
[2025-09-01 07:19:05] [INFO] [WOOCOMMERCE] ✅ Found mapping in Vedmg-woo-LMS: Product 50008 → Course 50007
[2025-09-01 07:19:05] [INFO] [WOOCOMMERCE] Matched product to course via Vedmg-woo-LMS mapping: Product ID 50008 → Course ID 50007
[2025-09-01 07:19:05] [INFO] [WOOCOMMERCE] Found course in VedMG database: tata motors (ID: 68)
[2025-09-01 07:19:05] [INFO] [WOOCOMMERCE] Course details - Google Classroom ID: 801060685359, Calendar ID: <EMAIL>
[2025-09-01 07:19:05] [INFO] [WOOCOMMERCE] ✅ Course has calendar_id - calendar sharing will be attempted
[2025-09-01 07:19:05] [INFO] [WOOCOMMERCE] Found course in VedMG database: tata motors (ID: 68)
[2025-09-01 07:19:05] [INFO] [WOOCOMMERCE] Course details - Google Classroom ID: 801060685359, Calendar ID: <EMAIL>
[2025-09-01 07:19:05] [INFO] [WOOCOMMERCE] ✅ Course has calendar_id - calendar sharing will be attempted
[2025-09-01 07:19:05] [INFO] [WOOCOMMERCE] Created new enrollment for student: <EMAIL>
[2025-09-01 07:19:12] [INFO] [EMAIL] Enrollment notification sent to: <EMAIL>
[2025-09-01 07:19:12] [INFO] [WOOCOMMERCE] ✅ Triggering calendar sharing for order status: processing
[2025-09-01 07:19:12] [INFO] [WOOCOMMERCE] Sharing calendar with student: <EMAIL>
[2025-09-01 07:19:12] [INFO] [WOOCOMMERCE] Making calendar sharing API call
[2025-09-01 07:19:14] [INFO] [WOOCOMMERCE] Calendar shared successfully
[2025-09-01 07:19:14] [INFO] [WOOCOMMERCE] Processed 1 course enrollments for order: 50011
[2025-09-01 07:19:26] [INFO] [API] API handlers initialized
[2025-09-01 07:19:26] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:19:26] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:19:26] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:19:26] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:19:26] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:19:26] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:19:26] [INFO] [CORE] Starting core initialization
[2025-09-01 07:19:26] [INFO] [CORE] Core initialization completed
[2025-09-01 07:19:27] [INFO] [API] API handlers initialized
[2025-09-01 07:19:27] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:19:27] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:19:27] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:19:27] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:19:27] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:19:27] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:19:27] [INFO] [CORE] Starting core initialization
[2025-09-01 07:19:27] [INFO] [CORE] Core initialization completed
[2025-09-01 07:19:56] [INFO] [API] API handlers initialized
[2025-09-01 07:19:56] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:19:56] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:19:56] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:19:56] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:19:56] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:19:56] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:19:56] [INFO] [CORE] Starting core initialization
[2025-09-01 07:19:56] [INFO] [ADMIN] Admin interface initialized
[2025-09-01 07:19:56] [INFO] [CORE] Admin functionality loaded
[2025-09-01 07:19:56] [INFO] [CORE] Core initialization completed
[2025-09-01 07:19:57] [INFO] [ADMIN] Settings initialized
[2025-09-01 07:20:00] [INFO] [API] API handlers initialized
[2025-09-01 07:20:01] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:20:01] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:20:01] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:20:01] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:20:01] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:20:01] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:20:01] [INFO] [CORE] Starting core initialization
[2025-09-01 07:20:01] [INFO] [ADMIN] Admin interface initialized
[2025-09-01 07:20:01] [INFO] [CORE] Admin functionality loaded
[2025-09-01 07:20:01] [INFO] [CORE] Core initialization completed
[2025-09-01 07:20:02] [INFO] [ADMIN] Admin menu created
[2025-09-01 07:20:02] [INFO] [ADMIN] Settings initialized
[2025-09-01 07:20:02] [INFO] [ADMIN] Admin assets enqueued for page: enrollments
[2025-09-01 07:20:02] [ACTION] [ADMIN] Admin action: Accessed enrollments page | User: localadmin (ID: 27)
[2025-09-01 07:20:02] [ACTION] [ADMIN] Admin action: Viewed student enrollments page | User: localadmin (ID: 27)
[2025-09-01 07:20:02] [DATABASE] [DB] Database SELECT on table: vedmg_student_enrollments | Details: Retrieved 10 enrollments (page 1 of 8)
[2025-09-01 07:20:02] [DATABASE] [DB] Database SELECT on table: classroom_options | Details: Retrieved 27 classroom options from database
[2025-09-01 07:20:13] [INFO] [API] API handlers initialized
[2025-09-01 07:20:14] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:20:14] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:20:14] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:20:14] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:20:14] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:20:14] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:20:14] [INFO] [CORE] Starting core initialization
[2025-09-01 07:20:14] [INFO] [ADMIN] Admin interface initialized
[2025-09-01 07:20:14] [INFO] [CORE] Admin functionality loaded
[2025-09-01 07:20:14] [INFO] [CORE] Core initialization completed
[2025-09-01 07:20:14] [INFO] [ADMIN] Admin menu created
[2025-09-01 07:20:14] [INFO] [ADMIN] Settings initialized
[2025-09-01 07:20:14] [INFO] [ADMIN] Admin assets enqueued for page: enrollments
[2025-09-01 07:20:15] [ACTION] [ADMIN] Admin action: Accessed enrollments page | User: localadmin (ID: 27)
[2025-09-01 07:20:15] [ACTION] [ADMIN] Admin action: Viewed student enrollments page | User: localadmin (ID: 27)
[2025-09-01 07:20:15] [DATABASE] [DB] Database SELECT on table: vedmg_student_enrollments | Details: Retrieved 1 enrollments (page 1 of 1)
[2025-09-01 07:20:15] [DATABASE] [DB] Database SELECT on table: classroom_options | Details: Retrieved 27 classroom options from database
[2025-09-01 07:20:22] [INFO] [API] API handlers initialized
[2025-09-01 07:20:22] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:20:22] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:20:22] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:20:22] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:20:22] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:20:22] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:20:22] [INFO] [CORE] Starting core initialization
[2025-09-01 07:20:22] [INFO] [ADMIN] Admin interface initialized
[2025-09-01 07:20:22] [INFO] [CORE] Admin functionality loaded
[2025-09-01 07:20:22] [INFO] [CORE] Core initialization completed
[2025-09-01 07:20:23] [INFO] [ADMIN] Settings initialized
[2025-09-01 07:20:49] [INFO] [API] API handlers initialized
[2025-09-01 07:20:49] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:20:49] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:20:49] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:20:49] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:20:49] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:20:49] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:20:49] [INFO] [CORE] Starting core initialization
[2025-09-01 07:20:49] [INFO] [ADMIN] Admin interface initialized
[2025-09-01 07:20:49] [INFO] [CORE] Admin functionality loaded
[2025-09-01 07:20:49] [INFO] [CORE] Core initialization completed
[2025-09-01 07:20:50] [INFO] [API] API handlers initialized
[2025-09-01 07:20:50] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:20:50] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:20:50] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:20:50] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:20:50] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:20:50] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:20:50] [INFO] [CORE] Starting core initialization
[2025-09-01 07:20:50] [INFO] [CORE] Core initialization completed
[2025-09-01 07:20:51] [INFO] [ADMIN] Settings initialized
[2025-09-01 07:20:51] [INFO] [SCHEDULE_LAB] Schedule lab request received
[2025-09-01 07:20:51] [INFO] [SCHEDULE_LAB] Nonce verification
[2025-09-01 07:20:51] [INFO] [SCHEDULE_LAB] Nonce verification passed
[2025-09-01 07:20:51] [INFO] [SCHEDULE_LAB] Session type: individual
[2025-09-01 07:20:51] [INFO] [SCHEDULE_LAB] Starting individual session scheduling
[2025-09-01 07:20:51] [INFO] [SCHEDULE_LAB] Processing individual session
[2025-09-01 07:20:51] [INFO] [SCHEDULE_LAB] Getting student data for ID: 152
[2025-09-01 07:20:51] [INFO] [SCHEDULE_LAB] Student data retrieved
[2025-09-01 07:20:51] [INFO] [SCHEDULE_LAB] Getting course data for ID: 68
[2025-09-01 07:20:51] [INFO] [SCHEDULE_LAB] Getting course data for course_id: 68 from table: wp_vedmg_courses
[2025-09-01 07:20:51] [INFO] [SCHEDULE_LAB] Course data retrieved successfully
[2025-09-01 07:20:51] [INFO] [SCHEDULE_LAB] Course data retrieved successfully
[2025-09-01 07:20:51] [INFO] [SCHEDULE_LAB] API Request: {
    "calendar_id": "<EMAIL>",
    "user_email": "<EMAIL>",
    "summary": "Live Session - tata motors",
    "location": "On Google Meet",
    "description": "this is tata motors. Please join at https:\/\/meet.google.com\/xfd-hwqc-gvj",
    "start_datetime": "2025-09-02T14:00:00+05:30",
    "end_datetime": "2025-09-14T15:00:00+05:30",
    "instructor_email": "<EMAIL>",
    "meeting_frequency": "FREQ=WEEKLY;BYDAY=MO,WE,FR;COUNT=3"
}
[2025-09-01 07:20:51] [INFO] [SCHEDULE_LAB] Making real API call to: https://gclassroom-839391304260.us-central1.run.app/share_invite
[2025-09-01 07:20:52] [INFO] [SCHEDULE_LAB] API Response - HTTP: 200, Body: {"kind": "calendar#event", "etag": "\"3513422505165214\"", "id": "ncmoe2ouhbmuug28f8vnh9rpg0", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=bmNtb2Uyb3VoYm11dWcyOGY4dm5oOXJwZzBfMjAyNTA5MDJUMDgzMDAwWiBjX2NsYXNzcm9vbWZhZjVmN2Y2QGc", "created": "2025-09-01T07:20:52.000Z", "updated": "2025-09-01T07:20:52.582Z", "summary": "Live Session - tata motors", "description": "this is tata motors. Please join at https://meet.google.com/xfd-hwqc-gvj", "location": "On Google Meet
[2025-09-01 07:20:52] [INFO] [SCHEDULE_LAB] Session stored successfully with ID: 65
[2025-09-01 07:20:53] [INFO] [DATABASE] Updated scheduling data for enrollment 82
[2025-09-01 07:20:53] [INFO] [DATABASE] Added session tracking record 12
[2025-09-01 07:20:53] [INFO] [SCHEDULE_LAB] Individual session completed successfully
[2025-09-01 07:21:12] [INFO] [API] API handlers initialized
[2025-09-01 07:21:12] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:21:12] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:21:12] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:21:12] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:21:12] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:21:12] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:21:12] [INFO] [CORE] Starting core initialization
[2025-09-01 07:21:12] [INFO] [ADMIN] Admin interface initialized
[2025-09-01 07:21:12] [INFO] [CORE] Admin functionality loaded
[2025-09-01 07:21:12] [INFO] [CORE] Core initialization completed
[2025-09-01 07:21:13] [INFO] [ADMIN] Admin menu created
[2025-09-01 07:21:13] [INFO] [ADMIN] Settings initialized
[2025-09-01 07:21:13] [INFO] [ADMIN] Admin assets enqueued for page: sessions
[2025-09-01 07:21:13] [ACTION] [ADMIN] Admin action: Accessed sessions page | User: localadmin (ID: 27)
[2025-09-01 07:21:14] [ACTION] [ADMIN] Admin action: Viewed class sessions page | User: localadmin (ID: 27)
[2025-09-01 07:21:14] [INFO] [DATABASE] Auto-updating session statuses. Current time: 2025-09-01 07:21:14
[2025-09-01 07:21:14] [DATABASE] [DB] Database SELECT on table: vedmg_class_sessions | Details: Retrieved 10 sessions (page 1 of 6)
[2025-09-01 07:21:14] [DATABASE] [DB] Database SELECT on table: classroom_options | Details: Retrieved 27 classroom options from database
[2025-09-01 07:21:14] [DATABASE] [DB] Database SELECT on table: vedmg_courses | Details: Retrieved 53 courses (page 1 of 1)
[2025-09-01 07:21:14] [DATABASE] [DB] Database SELECT on table: vedmg_class_sessions | Details: Retrieved 0 featured sessions
[2025-09-01 07:21:24] [INFO] [API] API handlers initialized
[2025-09-01 07:21:24] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:21:24] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:21:24] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:21:24] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:21:24] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:21:24] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:21:24] [INFO] [CORE] Starting core initialization
[2025-09-01 07:21:24] [INFO] [ADMIN] Admin interface initialized
[2025-09-01 07:21:24] [INFO] [CORE] Admin functionality loaded
[2025-09-01 07:21:24] [INFO] [CORE] Core initialization completed
[2025-09-01 07:21:25] [INFO] [ADMIN] Settings initialized
[2025-09-01 07:21:25] [ACTION] [ADMIN] Admin action: Featuring session: 65 | User: localadmin (ID: 27)
[2025-09-01 07:21:25] [DATABASE] [DB] Database UPDATE on table: vedmg_class_sessions | Details: Session 65 featured successfully
[2025-09-01 07:21:27] [INFO] [API] API handlers initialized
[2025-09-01 07:21:27] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:21:27] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:21:27] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:21:27] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:21:27] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:21:27] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:21:27] [INFO] [CORE] Starting core initialization
[2025-09-01 07:21:27] [INFO] [ADMIN] Admin interface initialized
[2025-09-01 07:21:27] [INFO] [CORE] Admin functionality loaded
[2025-09-01 07:21:27] [INFO] [CORE] Core initialization completed
[2025-09-01 07:21:28] [INFO] [API] API handlers initialized
[2025-09-01 07:21:29] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:21:29] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:21:29] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:21:29] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:21:29] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:21:29] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:21:29] [INFO] [CORE] Starting core initialization
[2025-09-01 07:21:29] [INFO] [CORE] Core initialization completed
[2025-09-01 07:21:29] [INFO] [ADMIN] Admin menu created
[2025-09-01 07:21:29] [INFO] [ADMIN] Settings initialized
[2025-09-01 07:21:29] [INFO] [ADMIN] Admin assets enqueued for page: sessions
[2025-09-01 07:21:29] [ACTION] [ADMIN] Admin action: Accessed sessions page | User: localadmin (ID: 27)
[2025-09-01 07:21:29] [ACTION] [ADMIN] Admin action: Viewed class sessions page | User: localadmin (ID: 27)
[2025-09-01 07:21:29] [INFO] [DATABASE] Auto-updating session statuses. Current time: 2025-09-01 07:21:29
[2025-09-01 07:21:29] [DATABASE] [DB] Database SELECT on table: vedmg_class_sessions | Details: Retrieved 10 sessions (page 1 of 6)
[2025-09-01 07:21:29] [DATABASE] [DB] Database SELECT on table: classroom_options | Details: Retrieved 27 classroom options from database
[2025-09-01 07:21:29] [DATABASE] [DB] Database SELECT on table: vedmg_courses | Details: Retrieved 53 courses (page 1 of 1)
[2025-09-01 07:21:29] [DATABASE] [DB] Database SELECT on table: vedmg_class_sessions | Details: Retrieved 1 featured sessions
[2025-09-01 07:22:22] [INFO] [API] API handlers initialized
[2025-09-01 07:22:22] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:22:22] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:22:22] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:22:22] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:22:22] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:22:22] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:22:22] [INFO] [CORE] Starting core initialization
[2025-09-01 07:22:22] [INFO] [ADMIN] Admin interface initialized
[2025-09-01 07:22:22] [INFO] [CORE] Admin functionality loaded
[2025-09-01 07:22:22] [INFO] [CORE] Core initialization completed
[2025-09-01 07:22:23] [INFO] [ADMIN] Settings initialized
[2025-09-01 07:22:23] [ACTION] [ADMIN] Admin action: Session deletion attempted for session 65 | User: localadmin (ID: 27)
[2025-09-01 07:22:23] [INFO] [CALENDAR] Attempting to delete calendar: <EMAIL> for instructor: <EMAIL>
[2025-09-01 07:22:23] [INFO] [API] Making API call
[2025-09-01 07:22:25] [INFO] [API] API response received
[2025-09-01 07:22:25] [INFO] [API] API call successful
[2025-09-01 07:22:25] [INFO] [CALENDAR] Successfully deleted calendar: <EMAIL>
[2025-09-01 07:22:25] [INFO] [CALENDAR] Updated 1 courses to remove calendar_id: <EMAIL>
[2025-09-01 07:22:25] [INFO] [CALENDAR] Successfully <NAME_EMAIL> for session 65
[2025-09-01 07:22:25] [INFO] [ADMIN] Session 65 deleted successfully
[2025-09-01 07:22:30] [INFO] [API] API handlers initialized
[2025-09-01 07:22:31] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:22:31] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:22:31] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:22:31] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:22:31] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:22:31] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:22:31] [INFO] [CORE] Starting core initialization
[2025-09-01 07:22:31] [INFO] [ADMIN] Admin interface initialized
[2025-09-01 07:22:31] [INFO] [CORE] Admin functionality loaded
[2025-09-01 07:22:31] [INFO] [CORE] Core initialization completed
[2025-09-01 07:22:32] [INFO] [API] API handlers initialized
[2025-09-01 07:22:32] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:22:32] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:22:32] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:22:32] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:22:32] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:22:32] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:22:32] [INFO] [CORE] Starting core initialization
[2025-09-01 07:22:32] [INFO] [CORE] Core initialization completed
[2025-09-01 07:22:32] [INFO] [ADMIN] Settings initialized
[2025-09-01 07:23:30] [INFO] [API] API handlers initialized
[2025-09-01 07:23:31] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:23:31] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:23:31] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:23:31] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:23:31] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:23:31] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:23:31] [INFO] [CORE] Starting core initialization
[2025-09-01 07:23:31] [INFO] [ADMIN] Admin interface initialized
[2025-09-01 07:23:31] [INFO] [CORE] Admin functionality loaded
[2025-09-01 07:23:31] [INFO] [CORE] Core initialization completed
[2025-09-01 07:23:32] [INFO] [API] API handlers initialized
[2025-09-01 07:23:32] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:23:32] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:23:32] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:23:32] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:23:32] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:23:32] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:23:32] [INFO] [CORE] Starting core initialization
[2025-09-01 07:23:32] [INFO] [CORE] Core initialization completed
[2025-09-01 07:23:32] [INFO] [ADMIN] Settings initialized
[2025-09-01 07:23:46] [INFO] [API] API handlers initialized
[2025-09-01 07:23:46] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:23:46] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:23:46] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:23:46] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:23:46] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:23:46] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:23:46] [INFO] [CORE] Starting core initialization
[2025-09-01 07:23:46] [INFO] [ADMIN] Admin interface initialized
[2025-09-01 07:23:46] [INFO] [CORE] Admin functionality loaded
[2025-09-01 07:23:46] [INFO] [CORE] Core initialization completed
[2025-09-01 07:23:47] [INFO] [ADMIN] Admin menu created
[2025-09-01 07:23:47] [INFO] [ADMIN] Settings initialized
[2025-09-01 07:23:47] [INFO] [ADMIN] Admin assets enqueued for page: dashboard
[2025-09-01 07:23:48] [ACTION] [ADMIN] Admin action: Accessed instructor sync page | User: localadmin (ID: 27)
[2025-09-01 07:23:48] [ACTION] [ADMIN] Admin action: Viewed instructor sync page | User: localadmin (ID: 27)
[2025-09-01 07:23:48] [INFO] [API] API handlers initialized
[2025-09-01 07:23:49] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:23:49] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:23:49] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:23:49] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:23:49] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:23:49] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:23:49] [INFO] [CORE] Starting core initialization
[2025-09-01 07:23:49] [INFO] [ADMIN] Admin interface initialized
[2025-09-01 07:23:49] [INFO] [CORE] Admin functionality loaded
[2025-09-01 07:23:49] [INFO] [CORE] Core initialization completed
[2025-09-01 07:23:50] [INFO] [ADMIN] Settings initialized
[2025-09-01 07:23:50] [INFO] [API] Getting all instructors for dropdown
[2025-09-01 07:23:50] [DATABASE] [DB] Database SELECT on table: instructors_paginated | Details: Retrieved 1000 instructors (page 1 of 1), total: 22
[2025-09-01 07:23:50] [INFO] [API] Found 22 instructors
[2025-09-01 07:23:56] [INFO] [API] API handlers initialized
[2025-09-01 07:23:56] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:23:56] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:23:56] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:23:56] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:23:56] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:23:56] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:23:56] [INFO] [CORE] Starting core initialization
[2025-09-01 07:23:56] [INFO] [ADMIN] Admin interface initialized
[2025-09-01 07:23:56] [INFO] [CORE] Admin functionality loaded
[2025-09-01 07:23:56] [INFO] [CORE] Core initialization completed
[2025-09-01 07:23:57] [INFO] [ADMIN] Admin menu created
[2025-09-01 07:23:57] [INFO] [ADMIN] Settings initialized
[2025-09-01 07:23:57] [INFO] [ADMIN] Admin assets enqueued for page: dashboard
[2025-09-01 07:23:57] [ACTION] [ADMIN] Admin action: Accessed instructor sync page | User: localadmin (ID: 27)
[2025-09-01 07:23:57] [ACTION] [ADMIN] Admin action: Viewed instructor sync page | User: localadmin (ID: 27)
[2025-09-01 07:23:58] [INFO] [API] API handlers initialized
[2025-09-01 07:23:58] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:23:58] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:23:58] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:23:58] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:23:58] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:23:58] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:23:58] [INFO] [CORE] Starting core initialization
[2025-09-01 07:23:58] [INFO] [ADMIN] Admin interface initialized
[2025-09-01 07:23:58] [INFO] [CORE] Admin functionality loaded
[2025-09-01 07:23:58] [INFO] [CORE] Core initialization completed
[2025-09-01 07:23:59] [INFO] [ADMIN] Settings initialized
[2025-09-01 07:23:59] [INFO] [API] Getting all instructors for dropdown
[2025-09-01 07:23:59] [DATABASE] [DB] Database SELECT on table: instructors_paginated | Details: Retrieved 1000 instructors (page 1 of 1), total: 22
[2025-09-01 07:23:59] [INFO] [API] Found 22 instructors
[2025-09-01 07:24:02] [INFO] [API] API handlers initialized
[2025-09-01 07:24:02] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:24:02] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:24:02] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:24:02] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:24:02] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:24:02] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:24:02] [INFO] [CORE] Starting core initialization
[2025-09-01 07:24:02] [INFO] [ADMIN] Admin interface initialized
[2025-09-01 07:24:02] [INFO] [CORE] Admin functionality loaded
[2025-09-01 07:24:02] [INFO] [CORE] Core initialization completed
[2025-09-01 07:24:03] [INFO] [ADMIN] Settings initialized
[2025-09-01 07:24:03] [INFO] [API] Starting Google Classroom sync for: <EMAIL> (Selected: Kushagra Mishra, ID: 24)
[2025-09-01 07:24:03] [INFO] [API] Found 36 courses for instructor ID: 24
[2025-09-01 07:24:03] [INFO] [API] Fetching courses from: https://gclassroom-839391304260.us-central1.run.app/list_courses for: <EMAIL>
[2025-09-01 07:24:04] [INFO] [API] Successfully fetched 8 courses from Google Classroom
[2025-09-01 07:24:04] [INFO] [API] Starting hybrid matching: Phase 1 - Google Classroom ID matching
[2025-09-01 07:24:04] [INFO] [API] Course matched by ID: flow testing → flow testing (ID: 799768958460)
[2025-09-01 07:24:04] [INFO] [API] Course matched by ID: re test → re test (ID: 800162928923)
[2025-09-01 07:24:04] [INFO] [API] Calendar ID updated from Google Classroom API (ID match)
[2025-09-01 07:24:04] [INFO] [API] Course matched by ID: live presentation → live presentation (ID: 800202844550)
[2025-09-01 07:24:04] [INFO] [API] Calendar ID updated from Google Classroom API (ID match)
[2025-09-01 07:24:04] [INFO] [API] Course matched by ID: entire flow test → entire flow test (ID: 800579699721)
[2025-09-01 07:24:04] [INFO] [API] Calendar ID updated from Google Classroom API (ID match)
[2025-09-01 07:24:04] [INFO] [API] Course matched by ID: final flow testing → final flow testing (ID: 780262248270)
[2025-09-01 07:24:04] [INFO] [API] Calendar ID updated from Google Classroom API (ID match)
[2025-09-01 07:24:04] [INFO] [API] Course matched by ID: video recording → video recording (ID: 800837934243)
[2025-09-01 07:24:04] [INFO] [API] Calendar ID updated from Google Classroom API (ID match)
[2025-09-01 07:24:04] [INFO] [API] Course matched by ID: tata motors → tata motors (ID: 801060685359)
[2025-09-01 07:24:04] [INFO] [API] Phase 1 complete: 7 exact ID matches. Starting Phase 2 - Name matching
[2025-09-01 07:24:04] [INFO] [API] Unmatched Google Classroom course (not creating): Overview of VedMG Academy
[2025-09-01 07:24:04] [INFO] [API] Google Classroom sync completed
[2025-09-01 07:24:10] [INFO] [API] API handlers initialized
[2025-09-01 07:24:11] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:24:11] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:24:11] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:24:11] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:24:11] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:24:11] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:24:11] [INFO] [CORE] Starting core initialization
[2025-09-01 07:24:11] [INFO] [ADMIN] Admin interface initialized
[2025-09-01 07:24:11] [INFO] [CORE] Admin functionality loaded
[2025-09-01 07:24:11] [INFO] [CORE] Core initialization completed
[2025-09-01 07:24:12] [INFO] [ADMIN] Admin menu created
[2025-09-01 07:24:13] [INFO] [ADMIN] Settings initialized
[2025-09-01 07:24:13] [INFO] [ADMIN] Admin assets enqueued for page: dashboard
[2025-09-01 07:24:13] [ACTION] [ADMIN] Admin action: Accessed instructor sync page | User: localadmin (ID: 27)
[2025-09-01 07:24:13] [ACTION] [ADMIN] Admin action: Viewed instructor sync page | User: localadmin (ID: 27)
[2025-09-01 07:24:13] [INFO] [API] API handlers initialized
[2025-09-01 07:24:14] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:24:14] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:24:14] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:24:14] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:24:14] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:24:14] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:24:14] [INFO] [CORE] Starting core initialization
[2025-09-01 07:24:14] [INFO] [ADMIN] Admin interface initialized
[2025-09-01 07:24:14] [INFO] [CORE] Admin functionality loaded
[2025-09-01 07:24:14] [INFO] [CORE] Core initialization completed
[2025-09-01 07:24:14] [INFO] [ADMIN] Settings initialized
[2025-09-01 07:24:14] [INFO] [API] Getting all instructors for dropdown
[2025-09-01 07:24:14] [DATABASE] [DB] Database SELECT on table: instructors_paginated | Details: Retrieved 1000 instructors (page 1 of 1), total: 22
[2025-09-01 07:24:14] [INFO] [API] Found 22 instructors
[2025-09-01 07:25:13] [INFO] [API] API handlers initialized
[2025-09-01 07:25:14] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:25:14] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:25:14] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:25:14] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:25:14] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:25:14] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:25:14] [INFO] [CORE] Starting core initialization
[2025-09-01 07:25:14] [INFO] [ADMIN] Admin interface initialized
[2025-09-01 07:25:14] [INFO] [CORE] Admin functionality loaded
[2025-09-01 07:25:14] [INFO] [CORE] Core initialization completed
[2025-09-01 07:25:15] [INFO] [API] API handlers initialized
[2025-09-01 07:25:15] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:25:15] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:25:15] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:25:15] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:25:15] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:25:15] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:25:15] [INFO] [CORE] Starting core initialization
[2025-09-01 07:25:15] [INFO] [CORE] Core initialization completed
[2025-09-01 07:25:16] [INFO] [ADMIN] Settings initialized
[2025-09-01 07:25:46] [INFO] [API] API handlers initialized
[2025-09-01 07:25:46] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:25:46] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:25:46] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:25:46] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:25:46] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:25:46] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:25:46] [INFO] [CORE] Starting core initialization
[2025-09-01 07:25:46] [INFO] [ADMIN] Admin interface initialized
[2025-09-01 07:25:46] [INFO] [CORE] Admin functionality loaded
[2025-09-01 07:25:46] [INFO] [CORE] Core initialization completed
[2025-09-01 07:25:47] [INFO] [API] API handlers initialized
[2025-09-01 07:25:48] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:25:48] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:25:48] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:25:48] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:25:48] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:25:48] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:25:48] [INFO] [CORE] Starting core initialization
[2025-09-01 07:25:48] [INFO] [CORE] Core initialization completed
[2025-09-01 07:25:48] [INFO] [ADMIN] Admin menu created
[2025-09-01 07:25:48] [INFO] [ADMIN] Settings initialized
[2025-09-01 07:25:48] [INFO] [ADMIN] Admin assets enqueued for page: enrollments
[2025-09-01 07:25:48] [ACTION] [ADMIN] Admin action: Accessed enrollments page | User: localadmin (ID: 27)
[2025-09-01 07:25:48] [ACTION] [ADMIN] Admin action: Viewed student enrollments page | User: localadmin (ID: 27)
[2025-09-01 07:25:48] [DATABASE] [DB] Database SELECT on table: vedmg_student_enrollments | Details: Retrieved 10 enrollments (page 1 of 8)
[2025-09-01 07:25:49] [DATABASE] [DB] Database SELECT on table: classroom_options | Details: Retrieved 27 classroom options from database
[2025-09-01 07:25:53] [INFO] [API] API handlers initialized
[2025-09-01 07:25:53] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:25:53] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:25:53] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:25:53] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:25:53] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:25:53] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:25:53] [INFO] [CORE] Starting core initialization
[2025-09-01 07:25:53] [INFO] [ADMIN] Admin interface initialized
[2025-09-01 07:25:53] [INFO] [CORE] Admin functionality loaded
[2025-09-01 07:25:53] [INFO] [CORE] Core initialization completed
[2025-09-01 07:25:54] [INFO] [ADMIN] Admin menu created
[2025-09-01 07:25:54] [INFO] [ADMIN] Settings initialized
[2025-09-01 07:25:54] [INFO] [ADMIN] Admin assets enqueued for page: enrollments
[2025-09-01 07:25:54] [ACTION] [ADMIN] Admin action: Accessed enrollments page | User: localadmin (ID: 27)
[2025-09-01 07:25:54] [ACTION] [ADMIN] Admin action: Viewed student enrollments page | User: localadmin (ID: 27)
[2025-09-01 07:25:54] [DATABASE] [DB] Database SELECT on table: vedmg_student_enrollments | Details: Retrieved 1 enrollments (page 1 of 1)
[2025-09-01 07:25:54] [DATABASE] [DB] Database SELECT on table: classroom_options | Details: Retrieved 27 classroom options from database
[2025-09-01 07:26:01] [INFO] [API] API handlers initialized
[2025-09-01 07:26:02] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:26:02] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:26:02] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:26:02] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:26:02] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:26:02] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:26:02] [INFO] [CORE] Starting core initialization
[2025-09-01 07:26:02] [INFO] [ADMIN] Admin interface initialized
[2025-09-01 07:26:02] [INFO] [CORE] Admin functionality loaded
[2025-09-01 07:26:02] [INFO] [CORE] Core initialization completed
[2025-09-01 07:26:03] [INFO] [ADMIN] Settings initialized
[2025-09-01 07:26:15] [INFO] [API] API handlers initialized
[2025-09-01 07:26:16] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:26:16] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:26:16] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:26:16] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:26:16] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:26:16] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:26:16] [INFO] [CORE] Starting core initialization
[2025-09-01 07:26:16] [INFO] [ADMIN] Admin interface initialized
[2025-09-01 07:26:16] [INFO] [CORE] Admin functionality loaded
[2025-09-01 07:26:16] [INFO] [CORE] Core initialization completed
[2025-09-01 07:26:17] [INFO] [ADMIN] Settings initialized
[2025-09-01 07:26:17] [INFO] [SCHEDULE_LAB] Schedule lab request received
[2025-09-01 07:26:17] [INFO] [SCHEDULE_LAB] Nonce verification
[2025-09-01 07:26:17] [INFO] [SCHEDULE_LAB] Nonce verification passed
[2025-09-01 07:26:17] [INFO] [SCHEDULE_LAB] Session type: class
[2025-09-01 07:26:17] [INFO] [SCHEDULE_LAB] Starting class session scheduling
[2025-09-01 07:26:17] [INFO] [SCHEDULE_LAB] Processing class-wide session
[2025-09-01 07:26:17] [INFO] [SCHEDULE_LAB] Getting course data for course_id: 68 from table: wp_vedmg_courses
[2025-09-01 07:26:17] [INFO] [SCHEDULE_LAB] Course data retrieved successfully
[2025-09-01 07:26:17] [INFO] [SCHEDULE_LAB] API Request: {
    "calendar_id": "<EMAIL>",
    "user_email": "<EMAIL>",
    "summary": "Live Session - tata motors",
    "location": "On Google Meet",
    "description": ". Please join at https:\/\/meet.google.com\/xfd-hwqc-gvj",
    "start_datetime": "2025-09-02T14:00:00+05:30",
    "end_datetime": "2025-09-02T15:00:00+05:30",
    "instructor_email": "<EMAIL>",
    "meeting_frequency": "FREQ=WEEKLY;COUNT=1"
}
[2025-09-01 07:26:17] [INFO] [SCHEDULE_LAB] Making real API call to: https://gclassroom-839391304260.us-central1.run.app/share_invite
[2025-09-01 07:26:18] [INFO] [SCHEDULE_LAB] API Response - HTTP: 500, Body: <!doctype html>
<html lang=en>
<title>500 Internal Server Error</title>
<h1>Internal Server Error</h1>
<p>The server encountered an internal error and was unable to complete your request. Either the server is overloaded or there is an error in the application.</p>

[2025-09-01 07:26:18] [ERROR] [SCHEDULE_LAB] API Error | Details: HTTP 500: <!doctype html>
<html lang=en>
<title>500 Internal Server Error</title>
<h1>Internal Server Error</h1>
<p>The server encountered an internal error and was unable to complete your request. Either the server is overloaded or there is an error in the application.</p>

[2025-09-01 07:26:18] [ERROR] [SCHEDULE_LAB] Class-wide session failed for student | Details: <EMAIL>: API returned error: HTTP 500
[2025-09-01 07:26:55] [INFO] [API] API handlers initialized
[2025-09-01 07:26:55] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:26:55] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:26:55] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:26:55] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:26:55] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:26:55] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:26:55] [INFO] [CORE] Starting core initialization
[2025-09-01 07:26:55] [INFO] [ADMIN] Admin interface initialized
[2025-09-01 07:26:55] [INFO] [CORE] Admin functionality loaded
[2025-09-01 07:26:55] [INFO] [CORE] Core initialization completed
[2025-09-01 07:26:56] [INFO] [API] API handlers initialized
[2025-09-01 07:26:56] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:26:56] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:26:56] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:26:56] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:26:56] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:26:56] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:26:56] [INFO] [CORE] Starting core initialization
[2025-09-01 07:26:56] [INFO] [ADMIN] Admin interface initialized
[2025-09-01 07:26:56] [INFO] [CORE] Admin functionality loaded
[2025-09-01 07:26:56] [INFO] [CORE] Core initialization completed
[2025-09-01 07:26:56] [INFO] [API] API handlers initialized
[2025-09-01 07:26:57] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:26:57] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:26:57] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:26:57] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:26:57] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:26:57] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:26:57] [INFO] [CORE] Starting core initialization
[2025-09-01 07:26:57] [INFO] [CORE] Core initialization completed
[2025-09-01 07:26:57] [INFO] [ADMIN] Settings initialized
[2025-09-01 07:26:57] [INFO] [ADMIN] Admin menu created
[2025-09-01 07:26:58] [INFO] [ADMIN] Settings initialized
[2025-09-01 07:26:58] [INFO] [ADMIN] Admin assets enqueued for page: sessions
[2025-09-01 07:26:58] [ACTION] [ADMIN] Admin action: Accessed sessions page | User: localadmin (ID: 27)
[2025-09-01 07:26:58] [ACTION] [ADMIN] Admin action: Viewed class sessions page | User: localadmin (ID: 27)
[2025-09-01 07:26:58] [INFO] [DATABASE] Auto-updating session statuses. Current time: 2025-09-01 07:26:58
[2025-09-01 07:26:58] [DATABASE] [DB] Database SELECT on table: vedmg_class_sessions | Details: Retrieved 10 sessions (page 1 of 6)
[2025-09-01 07:26:58] [DATABASE] [DB] Database SELECT on table: classroom_options | Details: Retrieved 27 classroom options from database
[2025-09-01 07:26:58] [DATABASE] [DB] Database SELECT on table: vedmg_courses | Details: Retrieved 53 courses (page 1 of 1)
[2025-09-01 07:26:58] [DATABASE] [DB] Database SELECT on table: vedmg_class_sessions | Details: Retrieved 0 featured sessions
[2025-09-01 07:27:59] [INFO] [API] API handlers initialized
[2025-09-01 07:28:00] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:28:00] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:28:00] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:28:00] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:28:00] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:28:00] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:28:00] [INFO] [CORE] Starting core initialization
[2025-09-01 07:28:00] [INFO] [ADMIN] Admin interface initialized
[2025-09-01 07:28:00] [INFO] [CORE] Admin functionality loaded
[2025-09-01 07:28:00] [INFO] [CORE] Core initialization completed
[2025-09-01 07:28:00] [INFO] [API] API handlers initialized
[2025-09-01 07:28:01] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:28:01] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:28:01] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:28:01] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:28:01] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:28:01] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:28:01] [INFO] [CORE] Starting core initialization
[2025-09-01 07:28:01] [INFO] [CORE] Core initialization completed
[2025-09-01 07:28:01] [INFO] [ADMIN] Settings initialized
[2025-09-01 07:29:59] [INFO] [API] API handlers initialized
[2025-09-01 07:30:00] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:30:00] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:30:00] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:30:00] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:30:00] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:30:00] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:30:00] [INFO] [CORE] Starting core initialization
[2025-09-01 07:30:00] [INFO] [ADMIN] Admin interface initialized
[2025-09-01 07:30:00] [INFO] [CORE] Admin functionality loaded
[2025-09-01 07:30:00] [INFO] [CORE] Core initialization completed
[2025-09-01 07:30:01] [INFO] [API] API handlers initialized
[2025-09-01 07:30:01] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:30:01] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:30:01] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:30:01] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:30:01] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:30:01] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:30:01] [INFO] [CORE] Starting core initialization
[2025-09-01 07:30:01] [INFO] [CORE] Core initialization completed
[2025-09-01 07:30:02] [INFO] [ADMIN] Settings initialized
[2025-09-01 07:32:00] [INFO] [API] API handlers initialized
[2025-09-01 07:32:01] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:32:01] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:32:01] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:32:01] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:32:01] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:32:01] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:32:01] [INFO] [CORE] Starting core initialization
[2025-09-01 07:32:01] [INFO] [ADMIN] Admin interface initialized
[2025-09-01 07:32:01] [INFO] [CORE] Admin functionality loaded
[2025-09-01 07:32:01] [INFO] [CORE] Core initialization completed
[2025-09-01 07:32:02] [INFO] [API] API handlers initialized
[2025-09-01 07:32:02] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:32:02] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:32:02] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:32:02] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:32:02] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:32:02] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:32:02] [INFO] [CORE] Starting core initialization
[2025-09-01 07:32:02] [INFO] [CORE] Core initialization completed
[2025-09-01 07:32:03] [INFO] [ADMIN] Settings initialized
[2025-09-01 07:34:01] [INFO] [API] API handlers initialized
[2025-09-01 07:34:02] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:34:02] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:34:02] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:34:02] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:34:02] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:34:02] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:34:02] [INFO] [CORE] Starting core initialization
[2025-09-01 07:34:02] [INFO] [ADMIN] Admin interface initialized
[2025-09-01 07:34:02] [INFO] [CORE] Admin functionality loaded
[2025-09-01 07:34:02] [INFO] [CORE] Core initialization completed
[2025-09-01 07:34:03] [INFO] [API] API handlers initialized
[2025-09-01 07:34:03] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:34:03] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:34:03] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:34:03] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:34:03] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:34:03] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:34:03] [INFO] [CORE] Starting core initialization
[2025-09-01 07:34:03] [INFO] [CORE] Core initialization completed
[2025-09-01 07:34:03] [INFO] [ADMIN] Settings initialized
[2025-09-01 07:36:02] [INFO] [API] API handlers initialized
[2025-09-01 07:36:03] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:36:03] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:36:03] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:36:03] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:36:03] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:36:03] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:36:03] [INFO] [CORE] Starting core initialization
[2025-09-01 07:36:03] [INFO] [ADMIN] Admin interface initialized
[2025-09-01 07:36:03] [INFO] [CORE] Admin functionality loaded
[2025-09-01 07:36:03] [INFO] [CORE] Core initialization completed
[2025-09-01 07:36:04] [INFO] [API] API handlers initialized
[2025-09-01 07:36:04] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:36:04] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-01 07:36:04] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-01 07:36:04] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-01 07:36:04] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-01 07:36:04] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-01 07:36:04] [INFO] [CORE] Starting core initialization
[2025-09-01 07:36:04] [INFO] [CORE] Core initialization completed
[2025-09-01 07:36:05] [INFO] [ADMIN] Settings initialized
