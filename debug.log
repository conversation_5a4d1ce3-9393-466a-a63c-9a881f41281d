[2025-08-31 10:26:59] [INFO] [API] API handlers initialized
[2025-08-31 10:26:59] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-08-31 10:26:59] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-08-31 10:26:59] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-08-31 10:26:59] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-08-31 10:26:59] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-08-31 10:26:59] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-08-31 10:26:59] [INFO] [CORE] Starting core initialization
[2025-08-31 10:26:59] [INFO] [ADMIN] Admin interface initialized
[2025-08-31 10:26:59] [INFO] [CORE] Admin functionality loaded
[2025-08-31 10:26:59] [INFO] [CORE] Core initialization completed
[2025-08-31 10:27:00] [INFO] [ADMIN] Admin menu created
[2025-08-31 10:27:00] [INFO] [ADMIN] Settings initialized
[2025-08-31 10:27:00] [INFO] [ADMIN] Admin assets enqueued for page: courses
[2025-08-31 10:27:01] [ACTION] [ADMIN] Admin action: Accessed courses page | User: localadmin (ID: 27)
[2025-08-31 10:27:01] [ACTION] [ADMIN] Admin action: Viewed courses management page | User: localadmin (ID: 27)
[2025-08-31 10:27:01] [DATABASE] [DB] Database SELECT on table: vedmg_courses | Details: Retrieved 10 courses (page 1 of 6)
[2025-08-31 10:27:01] [DATABASE] [DB] Database SELECT on table: classroom_options | Details: Retrieved 26 classroom options from database
[2025-08-31 10:27:09] [INFO] [API] API handlers initialized
[2025-08-31 10:27:10] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-08-31 10:27:10] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-08-31 10:27:10] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-08-31 10:27:10] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-08-31 10:27:10] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-08-31 10:27:10] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-08-31 10:27:10] [INFO] [CORE] Starting core initialization
[2025-08-31 10:27:10] [INFO] [ADMIN] Admin interface initialized
[2025-08-31 10:27:10] [INFO] [CORE] Admin functionality loaded
[2025-08-31 10:27:10] [INFO] [CORE] Core initialization completed
[2025-08-31 10:27:11] [INFO] [ADMIN] Admin menu created
[2025-08-31 10:27:11] [INFO] [ADMIN] Settings initialized
[2025-08-31 10:27:11] [INFO] [ADMIN] Admin assets enqueued for page: sessions
[2025-08-31 10:27:11] [ACTION] [ADMIN] Admin action: Accessed sessions page | User: localadmin (ID: 27)
[2025-08-31 10:27:11] [ACTION] [ADMIN] Admin action: Viewed class sessions page | User: localadmin (ID: 27)
[2025-08-31 10:27:11] [INFO] [DATABASE] Auto-updating session statuses. Current time: 2025-08-31 10:27:11
[2025-08-31 10:27:11] [DATABASE] [DB] Database SELECT on table: vedmg_class_sessions | Details: Retrieved 10 sessions (page 1 of 6)
[2025-08-31 10:27:11] [DATABASE] [DB] Database SELECT on table: classroom_options | Details: Retrieved 26 classroom options from database
[2025-08-31 10:27:11] [DATABASE] [DB] Database SELECT on table: vedmg_courses | Details: Retrieved 52 courses (page 1 of 1)
[2025-08-31 10:27:11] [DATABASE] [DB] Database SELECT on table: vedmg_class_sessions | Details: Retrieved 0 featured sessions
[2025-08-31 10:27:27] [INFO] [API] API handlers initialized
[2025-08-31 10:27:28] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-08-31 10:27:28] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-08-31 10:27:28] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-08-31 10:27:28] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-08-31 10:27:28] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-08-31 10:27:28] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-08-31 10:27:28] [INFO] [CORE] Starting core initialization
[2025-08-31 10:27:28] [INFO] [ADMIN] Admin interface initialized
[2025-08-31 10:27:28] [INFO] [CORE] Admin functionality loaded
[2025-08-31 10:27:28] [INFO] [CORE] Core initialization completed
[2025-08-31 10:27:29] [INFO] [API] API handlers initialized
[2025-08-31 10:27:29] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-08-31 10:27:29] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-08-31 10:27:29] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-08-31 10:27:29] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-08-31 10:27:29] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-08-31 10:27:29] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-08-31 10:27:29] [INFO] [CORE] Starting core initialization
[2025-08-31 10:27:29] [INFO] [CORE] Core initialization completed
[2025-08-31 10:27:29] [INFO] [ADMIN] Settings initialized
[2025-08-31 10:27:29] [ACTION] [ADMIN] Admin action: Session deletion attempted for session 62 | User: localadmin (ID: 27)
[2025-08-31 10:27:29] [INFO] [CALENDAR] Attempting to delete calendar: <EMAIL> for instructor: <EMAIL>
[2025-08-31 10:27:29] [INFO] [API] Making API call
[2025-08-31 10:27:30] [INFO] [API] API response received
[2025-08-31 10:27:30] [ERROR] [API] HTTP error response | Details: Array
[2025-08-31 10:27:30] [ERROR] [CALENDAR] Calendar deletion failed: HTTP 500 error: 500 Internal Server Error | Details: Array
[2025-08-31 10:27:30] [ERROR] [CALENDAR] Failed to <NAME_EMAIL> for session 62
[2025-08-31 10:27:35] [INFO] [API] API handlers initialized
[2025-08-31 10:27:35] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-08-31 10:27:35] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-08-31 10:27:35] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-08-31 10:27:35] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-08-31 10:27:35] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-08-31 10:27:35] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-08-31 10:27:35] [INFO] [CORE] Starting core initialization
[2025-08-31 10:27:35] [INFO] [ADMIN] Admin interface initialized
[2025-08-31 10:27:35] [INFO] [CORE] Admin functionality loaded
[2025-08-31 10:27:35] [INFO] [CORE] Core initialization completed
[2025-08-31 10:27:36] [INFO] [ADMIN] Admin menu created
[2025-08-31 10:27:36] [INFO] [ADMIN] Settings initialized
[2025-08-31 10:27:36] [INFO] [ADMIN] Admin assets enqueued for page: sessions
[2025-08-31 10:27:36] [ACTION] [ADMIN] Admin action: Accessed sessions page | User: localadmin (ID: 27)
[2025-08-31 10:27:36] [ACTION] [ADMIN] Admin action: Viewed class sessions page | User: localadmin (ID: 27)
[2025-08-31 10:27:36] [INFO] [DATABASE] Auto-updating session statuses. Current time: 2025-08-31 10:27:36
[2025-08-31 10:27:36] [DATABASE] [DB] Database SELECT on table: vedmg_class_sessions | Details: Retrieved 10 sessions (page 1 of 6)
[2025-08-31 10:27:36] [DATABASE] [DB] Database SELECT on table: classroom_options | Details: Retrieved 26 classroom options from database
[2025-08-31 10:27:36] [DATABASE] [DB] Database SELECT on table: vedmg_courses | Details: Retrieved 52 courses (page 1 of 1)
[2025-08-31 10:27:36] [DATABASE] [DB] Database SELECT on table: vedmg_class_sessions | Details: Retrieved 0 featured sessions
[2025-08-31 10:28:37] [INFO] [API] API handlers initialized
[2025-08-31 10:28:37] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-08-31 10:28:37] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-08-31 10:28:37] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-08-31 10:28:37] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-08-31 10:28:37] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-08-31 10:28:37] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-08-31 10:28:37] [INFO] [CORE] Starting core initialization
[2025-08-31 10:28:37] [INFO] [ADMIN] Admin interface initialized
[2025-08-31 10:28:37] [INFO] [CORE] Admin functionality loaded
[2025-08-31 10:28:37] [INFO] [CORE] Core initialization completed
[2025-08-31 10:28:38] [INFO] [API] API handlers initialized
[2025-08-31 10:28:38] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-08-31 10:28:38] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-08-31 10:28:38] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-08-31 10:28:38] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-08-31 10:28:38] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-08-31 10:28:38] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-08-31 10:28:38] [INFO] [CORE] Starting core initialization
[2025-08-31 10:28:38] [INFO] [CORE] Core initialization completed
[2025-08-31 10:28:39] [INFO] [ADMIN] Settings initialized
