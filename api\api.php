<?php
/**
 * VedMG ClassRoom API Handler
 * 
 * This file handles all API-related operations for the plugin.
 * It serves as the central point for Google Classroom API and other external API calls.
 * 
 * @package VedMG_ClassRoom
 * <AUTHOR>
 * @version 1.0
 */

// Prevent direct access to this file
if (!defined('ABSPATH')) {
    exit('Direct access denied.');
}

/**
 * VedMG ClassRoom API Class
 * 
 * Handles all API operations and external service integrations
 */
class VedMG_ClassRoom_API {
    
    /**
     * @var string Google Classroom API endpoint
     */
    private static $google_classroom_api = 'https://gclassroom-839391304260.us-central1.run.app';
    
    /**
     * @var string Google Classroom API key
     */
    private static $api_key = 'G$$gle@VedMG!@#';

    /**
     * @var bool Development mode - enables mock responses for testing
     */
    private static $development_mode = false;
    
    /**
     * Initialize API functionality
     */
    public static function init() {
        // Add AJAX handlers for API operations
        add_action('wp_ajax_vedmg_sync_google_classroom', array(__CLASS__, 'handle_sync_google_classroom'));
        add_action('wp_ajax_vedmg_get_all_instructors', array(__CLASS__, 'handle_get_all_instructors'));

        // Add AJAX handlers for new classroom management operations
        add_action('wp_ajax_vedmg_create_classroom', array(__CLASS__, 'handle_create_classroom'));
        add_action('wp_ajax_vedmg_archive_classroom', array(__CLASS__, 'handle_archive_classroom'));
        add_action('wp_ajax_vedmg_delete_classroom', array(__CLASS__, 'handle_delete_classroom'));
        add_action('wp_ajax_vedmg_delete_calendar', array(__CLASS__, 'handle_delete_calendar'));

        // Add AJAX handler for meeting link functionality
        add_action('wp_ajax_vedmg_save_meeting_link', array(__CLASS__, 'handle_save_meeting_link'));
        add_action('wp_ajax_vedmg_get_course_data', array(__CLASS__, 'handle_get_course_data'));
        
        vedmg_log_info('API', 'API handlers initialized');
    }
    
    /**
     * Sync Google Classroom data for selected instructor
     * ENHANCED WORKFLOW:
     * 1. Instructor selection is mandatory (for course attribution)
     * 2. Email can be overridden (optional)
     * 3. Single word matching between LMS and Google Classroom courses
     * 4. All courses attributed to selected instructor regardless of email used
     */
    public static function handle_sync_google_classroom() {
        // Verify nonce for security
        if (!wp_verify_nonce($_POST['nonce'], 'vedmg_classroom_nonce')) {
            wp_send_json_error('Security check failed');
            return;
        }
        
        $instructor_email = sanitize_email($_POST['instructor_email']);
        $instructor_name = sanitize_text_field($_POST['instructor_name'] ?? '');
        $instructor_id = intval($_POST['instructor_id'] ?? 0);
        
        if (empty($instructor_email)) {
            wp_send_json_error('Instructor email is required');
            return;
        }
        
        if (empty($instructor_name) || $instructor_id <= 0) {
            wp_send_json_error('Valid instructor selection is required');
            return;
        }
        
        vedmg_log_info('API', 'Starting Google Classroom sync for: ' . $instructor_email . ' (Selected: ' . $instructor_name . ', ID: ' . $instructor_id . ')');
        
        try {
            // Step 1: Get selected instructor's courses from database
            $instructor_courses = self::get_instructor_courses_by_id($instructor_id);

            // Step 1.5: Check if there are any existing LMS courses to match against
            if (empty($instructor_courses)) {
                wp_send_json_error('No existing LMS courses found for this instructor. Please sync with MasterStudy LMS first to create courses before syncing with Google Classroom.');
                return;
            }

            // Step 2: Fetch courses from Google Classroom API using provided email
            $google_courses = self::fetch_google_classroom_courses($instructor_email);

            if ($google_courses === false) {
                wp_send_json_error('Failed to fetch courses from Google Classroom API. Please check the email address or API connectivity.');
                return;
            }

            if (empty($google_courses)) {
                wp_send_json_error('No courses found in Google Classroom for this email address. Please verify the email or check if the instructor has any courses in Google Classroom.');
                return;
            }

            // Step 3: Match courses using hybrid matching and update database
            $match_result = self::enhanced_match_and_update_courses($instructor_courses, $google_courses, $instructor_id, $instructor_name, $instructor_email);

            vedmg_log_info('API', 'Google Classroom sync completed', json_encode($match_result));

            // Generate appropriate response message
            if ($match_result['matches'] === 0) {
                // No matches found - provide detailed information
                $error_message = self::generate_detailed_mismatch_message($instructor_name, $instructor_email, $instructor_courses, $google_courses);
                wp_send_json_error($error_message);
                return;
            }

            // Success with matches
            $success_message = "Google Classroom sync completed successfully! Found {$match_result['matches']} matches.";

            // Add note about unmatched courses if any
            if (!empty($match_result['unmatched_google_courses'])) {
                $unmatched_count = count($match_result['unmatched_google_courses']);
                $success_message .= " Note: $unmatched_count Google Classroom courses were not matched and will not be created automatically.";
            }

            wp_send_json_success(array(
                'message' => $success_message,
                'instructor_email' => $instructor_email,
                'instructor_name' => $instructor_name,
                'database_courses' => count($instructor_courses),
                'google_courses' => count($google_courses),
                'matches_found' => $match_result['matches'],
                'courses_updated' => $match_result['updated'],
                'created' => $match_result['created'],
                'matched_details' => $match_result['details'],
                'unmatched_google_courses' => $match_result['unmatched_google_courses'] ?? []
            ));
            
        } catch (Exception $e) {
            vedmg_log_error('API', 'Google Classroom sync failed', $e->getMessage());
            wp_send_json_error('Sync failed: ' . $e->getMessage());
        }
    }
    
    /**
     * Handle get all instructors request
     */
    public static function handle_get_all_instructors() {
        // Verify nonce for security
        if (!wp_verify_nonce($_POST['nonce'], 'vedmg_classroom_nonce')) {
            wp_send_json_error('Security check failed');
        }
        
        vedmg_log_info('API', 'Getting all instructors for dropdown');
        
        try {
            // Get all instructors using database helper (same as instructor roster page)
            $instructors_data = VedMG_ClassRoom_Database_Helper::get_instructors_paginated(1, 1000, '', '');
            $instructors = $instructors_data['instructors'];
            
            // Format instructors for dropdown
            $formatted_instructors = array();
            foreach ($instructors as $instructor) {
                $formatted_instructors[] = array(
                    'instructor_id' => $instructor->instructor_id,
                    'instructor_name' => $instructor->instructor_name,
                    'instructor_email' => $instructor->instructor_email,
                    'specialization' => $instructor->specialization ?: 'General',
                    'course_count' => intval($instructor->course_count)
                );
            }
            
            vedmg_log_info('API', 'Found ' . count($formatted_instructors) . ' instructors');
            
            wp_send_json_success(array(
                'instructors' => $formatted_instructors,
                'message' => 'Instructors retrieved successfully'
            ));
            
        } catch (Exception $e) {
            vedmg_log_error('API', 'Failed to get instructors', $e->getMessage());
            wp_send_json_error('Failed to get instructors: ' . $e->getMessage());
        }
    }
    
    /**
     * Fetch courses from Google Classroom API for specific instructor
     * 
     * @param string $instructor_email Instructor email address
     * @return array|false Array of courses or false on failure
     */
    private static function fetch_google_classroom_courses($instructor_email) {
        $api_url = self::$google_classroom_api . '/list_courses';
        
        vedmg_log_info('API', 'Fetching courses from: ' . $api_url . ' for: ' . $instructor_email);
        
        $post_data = json_encode([
            'instructor_email' => $instructor_email
        ]);
        
        $response = wp_remote_post($api_url, array(
            'timeout' => 30,
            'headers' => array(
                'Content-Type' => 'application/json',
                'x-api-key' => self::$api_key,
                'Content-Length' => strlen($post_data)
            ),
            'body' => $post_data
        ));
        
        if (is_wp_error($response)) {
            vedmg_log_error('API', 'Failed to fetch Google Classroom courses', $response->get_error_message());
            return false;
        }
        
        $response_code = wp_remote_retrieve_response_code($response);
        $response_body = wp_remote_retrieve_body($response);
        
        if ($response_code !== 200) {
            vedmg_log_error('API', 'Google Classroom API returned error code: ' . $response_code);
            vedmg_log_error('API', 'Response body: ' . $response_body);
            return false;
        }
        
        $courses_data = json_decode($response_body, true);
        
        if (json_last_error() !== JSON_ERROR_NONE) {
            vedmg_log_error('API', 'Failed to parse Google Classroom API response');
            return false;
        }
        
        vedmg_log_info('API', 'Successfully fetched ' . count($courses_data) . ' courses from Google Classroom');
        
        return $courses_data;
    }
    
    /**
     * Sync courses to database
     * 
     * @param array $google_courses Courses from Google Classroom API
     * @param string $instructor_email Instructor email
     * @return array Sync results
     */
    private static function sync_courses_to_database($google_courses, $instructor_email) {
        global $wpdb;
        
        $courses_table = $wpdb->prefix . 'vedmg_courses';
        $synced = 0;
        $new = 0;
        $updated = 0;
        
        // Get instructor data
        $instructor_data = self::get_instructor_by_email($instructor_email);
        
        foreach ($google_courses as $google_course) {
            $google_classroom_id = $google_course['id'] ?? '';
            $course_name = $google_course['name'] ?? '';
            $course_description = $google_course['descriptionHeading'] ?? '';
            $google_classroom_link = $google_course['alternateLink'] ?? '';
            
            // Check if course already exists
            $existing_course = $wpdb->get_row($wpdb->prepare(
                "SELECT course_id FROM $courses_table WHERE google_classroom_id = %s",
                $google_classroom_id
            ));
            
            if ($existing_course) {
                // Update existing course
                $update_result = $wpdb->update(
                    $courses_table,
                    array(
                        'course_name' => $course_name,
                        'course_description' => $course_description,
                        'instructor_email' => $instructor_email,
                        'calendar_id' => isset($course['calendarId']) ? $course['calendarId'] : null,
                        'google_classroom_link' => $google_classroom_link,
                        'classroom_status' => 'active',
                        'updated_date' => current_time('mysql')
                    ),
                    array('google_classroom_id' => $google_classroom_id),
                    array('%s', '%s', '%s', '%s', '%s', '%s', '%s'),
                    array('%s')
                );
                
                if ($update_result !== false) {
                    $updated++;
                    vedmg_log_info('API', 'Updated course: ' . $course_name);
                }
            } else {
                // Insert new course
                $insert_result = $wpdb->insert(
                    $courses_table,
                    array(
                        'google_classroom_id' => $google_classroom_id,
                        'course_name' => $course_name,
                        'course_description' => $course_description,
                        'instructor_id' => $instructor_data ? $instructor_data->instructor_id : 0,
                        'instructor_name' => $instructor_data ? $instructor_data->instructor_name : 'Unknown',
                        'instructor_email' => $instructor_email,
                        'calendar_id' => isset($course['calendarId']) ? $course['calendarId'] : null,
                        'google_classroom_link' => $google_classroom_link,
                        'classroom_status' => 'active',
                        'course_status' => 'published'
                    ),
                    array('%s', '%s', '%s', '%d', '%s', '%s', '%s', '%s', '%s', '%s')
                );
                
                if ($insert_result !== false) {
                    $new++;
                    vedmg_log_info('API', 'Inserted new course: ' . $course_name);
                }
            }
            
            $synced++;
        }
        
        return array(
            'synced' => $synced,
            'new' => $new,
            'updated' => $updated
        );
    }
    
    /**
     * Get instructor's courses from database by instructor ID
     * 
     * @param int $instructor_id Instructor ID
     * @return array Array of instructor's courses
     */
    private static function get_instructor_courses_by_id($instructor_id) {
        global $wpdb;
        
        $courses_table = $wpdb->prefix . 'vedmg_courses';
        
        // Get all courses for this instructor ID, prioritizing pending status
        $instructor_courses = $wpdb->get_results($wpdb->prepare(
            "SELECT course_id, course_name, instructor_email, classroom_status, 
                    google_classroom_id, instructor_name, instructor_id
             FROM $courses_table 
             WHERE instructor_id = %d
             ORDER BY 
                CASE classroom_status 
                    WHEN 'pending' THEN 1 
                    WHEN 'created' THEN 2 
                    WHEN 'active' THEN 3 
                    ELSE 4 
                END",
            $instructor_id
        ));
        
        vedmg_log_info('API', 'Found ' . count($instructor_courses) . ' courses for instructor ID: ' . $instructor_id);
        
        return $instructor_courses ?: array();
    }
    
    /**
     * Get instructor's courses from database (prioritize pending courses)
     * 
     * @param string $instructor_email Instructor email
     * @return array Array of instructor's courses
     */
    private static function get_instructor_courses($instructor_email) {
        global $wpdb;
        
        $courses_table = $wpdb->prefix . 'vedmg_courses';
        
        // Get all courses for this instructor, prioritizing pending status
        $instructor_courses = $wpdb->get_results($wpdb->prepare(
            "SELECT course_id, course_name, instructor_email, classroom_status, 
                    google_classroom_id, instructor_name
             FROM $courses_table 
             WHERE instructor_email = %s 
             ORDER BY 
                CASE classroom_status 
                    WHEN 'pending' THEN 1 
                    WHEN 'created' THEN 2 
                    WHEN 'active' THEN 3 
                    ELSE 4 
                END",
            $instructor_email
        ));
        
        vedmg_log_info('API', 'Found ' . count($instructor_courses) . ' courses for instructor: ' . $instructor_email);
        
        return $instructor_courses ?: array();
    }
    
    /**
     * Get pending courses for an instructor (Legacy method - keeping for compatibility)
     * 
     * @param string $instructor_email Instructor email
     * @return array Array of pending courses
     */
    private static function get_pending_courses($instructor_email) {
        global $wpdb;
        
        $courses_table = $wpdb->prefix . 'vedmg_courses';
        
        $pending_courses = $wpdb->get_results($wpdb->prepare(
            "SELECT course_id, course_name, instructor_email 
             FROM $courses_table 
             WHERE instructor_email = %s 
             AND classroom_status = 'pending'",
            $instructor_email
        ));
        
        return $pending_courses ?: array();
    }
    
    /**
     * Enhanced match and update courses using hybrid approach: Google Classroom ID + Name matching
     * 1. First tries exact Google Classroom ID matching for maximum accuracy
     * 2. Falls back to single-word name matching for courses without Google IDs
     * 3. Never creates new courses - only updates existing ones
     *
     * @param array $instructor_courses Instructor's courses from database
     * @param array $google_courses Courses from Google Classroom API
     * @param int $instructor_id Selected instructor ID (for attribution)
     * @param string $instructor_name Selected instructor name (for attribution)
     * @param string $api_email Email used for Google Classroom API
     * @return array Match results
     */
    private static function enhanced_match_and_update_courses($instructor_courses, $google_courses, $instructor_id, $instructor_name, $api_email) {
        global $wpdb;
        
        $courses_table = $wpdb->prefix . 'vedmg_courses';
        $matches = 0;
        $updated = 0;
        $created = 0;
        $details = array();
        $processed_google_courses = array();
        $processed_db_courses = array();

        vedmg_log_info('API', 'Starting hybrid matching: Phase 1 - Google Classroom ID matching');

        // Phase 1: Exact Google Classroom ID matching (highest priority)
        foreach ($instructor_courses as $db_course) {
            // Skip if this DB course was already matched
            if (in_array($db_course->course_id, $processed_db_courses)) {
                continue;
            }

            // Only process courses that have a Google Classroom ID
            if (empty($db_course->google_classroom_id)) {
                continue;
            }

            foreach ($google_courses as $google_course) {
                $google_course_id = $google_course['id'];

                // Skip if this Google course was already matched
                if (in_array($google_course_id, $processed_google_courses)) {
                    continue;
                }

                // Check for exact Google Classroom ID match
                if ($db_course->google_classroom_id === $google_course_id) {
                    // Exact ID match found!
                    $update_result = $wpdb->update(
                        $courses_table,
                        array(
                            'google_classroom_link' => $google_course['alternateLink'] ?? '',
                            'classroom_status' => 'active',
                            'course_description' => $google_course['descriptionHeading'] ?? $db_course->course_name,
                            'updated_date' => current_time('mysql')
                        ),
                        array('course_id' => $db_course->course_id),
                        array('%s', '%s', '%s', '%s'),
                        array('%d')
                    );

                    if ($update_result !== false) {
                        $matches++;
                        $updated++;
                        $processed_google_courses[] = $google_course_id;
                        $processed_db_courses[] = $db_course->course_id;

                        $details[] = array(
                            'action' => 'updated_by_id',
                            'db_course' => $db_course->course_name,
                            'google_course' => $google_course['name'],
                            'google_classroom_id' => $google_course['id'],
                            'matched_word' => 'Exact Google Classroom ID',
                            'status_change' => $db_course->classroom_status . ' → active',
                            'attributed_to' => $instructor_name
                        );

                        vedmg_log_info('API', 'Course matched by ID: ' . $db_course->course_name . ' → ' . $google_course['name'] . ' (ID: ' . $google_course_id . ')');
                    }

                    break; // Move to next DB course after finding exact match
                }
            }
        }

        vedmg_log_info('API', 'Phase 1 complete: ' . $matches . ' exact ID matches. Starting Phase 2 - Name matching');

        // Phase 2: Name-based matching for remaining courses
        foreach ($instructor_courses as $db_course) {
            // Skip if this DB course was already matched by ID
            if (in_array($db_course->course_id, $processed_db_courses)) {
                continue;
            }

            foreach ($google_courses as $google_course) {
                $google_course_id = $google_course['id'];

                // Skip if this Google course was already matched
                if (in_array($google_course_id, $processed_google_courses)) {
                    continue;
                }

                // Use enhanced single-word matching
                $match_result = self::enhanced_single_word_match($db_course->course_name, $google_course['name']);

                if ($match_result['match']) {
                    // Update database with Google Classroom data (including the Google ID)
                    $update_result = $wpdb->update(
                        $courses_table,
                        array(
                            'google_classroom_id' => $google_course['id'], // Store the Google ID for future exact matching
                            'calendar_id' => isset($google_course['calendarId']) ? $google_course['calendarId'] : null,
                            'google_classroom_link' => $google_course['alternateLink'] ?? '',
                            'classroom_status' => 'active',
                            'course_description' => $google_course['descriptionHeading'] ?? $db_course->course_name,
                            'updated_date' => current_time('mysql')
                        ),
                        array('course_id' => $db_course->course_id),
                        array('%s', '%s', '%s', '%s', '%s', '%s'),
                        array('%d')
                    );

                    if ($update_result !== false) {
                        $matches++;
                        $updated++;
                        $processed_google_courses[] = $google_course_id;
                        $processed_db_courses[] = $db_course->course_id;

                        $details[] = array(
                            'action' => 'updated_by_name',
                            'db_course' => $db_course->course_name,
                            'google_course' => $google_course['name'],
                            'google_classroom_id' => $google_course['id'],
                            'matched_word' => $match_result['matched_word'],
                            'status_change' => $db_course->classroom_status . ' → active',
                            'attributed_to' => $instructor_name
                        );

                        vedmg_log_info('API', 'Course matched by name: ' . $db_course->course_name . ' → ' . $google_course['name'] . ' (matched on: ' . $match_result['matched_word'] . ')');
                    }

                    break; // Move to next DB course after finding a match
                }
            }
        }
        
        // Phase 2: Log unmatched Google Classroom courses (but don't create them)
        $unmatched_courses = array();
        foreach ($google_courses as $google_course) {
            $google_course_id = $google_course['id'];

            // Skip if this Google course was already matched
            if (in_array($google_course_id, $processed_google_courses)) {
                continue;
            }

            // Log unmatched course for reporting
            $unmatched_courses[] = $google_course['name'];
            vedmg_log_info('API', 'Unmatched Google Classroom course (not creating): ' . $google_course['name']);
        }

        // Add unmatched courses info to details if any
        if (!empty($unmatched_courses)) {
            $details[] = array(
                'action' => 'skipped',
                'db_course' => 'N/A',
                'google_course' => 'Unmatched courses: ' . implode(', ', $unmatched_courses),
                'google_classroom_id' => 'N/A',
                'matched_word' => 'No match found',
                'status_change' => 'skipped (no LMS course to match)',
                'attributed_to' => $instructor_name
            );
        }
        
        return array(
            'matches' => $matches,
            'updated' => $updated,
            'created' => $created,
            'details' => $details,
            'unmatched_google_courses' => $unmatched_courses
        );
    }

    /**
     * Generate detailed mismatch message when no courses match
     *
     * @param string $instructor_name Instructor name
     * @param string $instructor_email Instructor email
     * @param array $lms_courses LMS courses
     * @param array $google_courses Google Classroom courses
     * @return string Detailed error message
     */
    private static function generate_detailed_mismatch_message($instructor_name, $instructor_email, $lms_courses, $google_courses) {
        $message = "Sync completed but no course names matched between LMS and Google Classroom for instructor '$instructor_name'.\n\n";

        $message .= "📚 LMS Courses found:\n";
        foreach ($lms_courses as $course) {
            $google_id_status = $course->google_classroom_id ? " (Google ID: {$course->google_classroom_id})" : "";
            $message .= "   • {$course->course_name}$google_id_status\n";
        }

        $message .= "\n🎓 Google Classroom courses found:\n";
        foreach ($google_courses as $course) {
            $message .= "   • {$course['name']} (ID: {$course['id']})\n";
        }

        $message .= "\n💡 Suggestion: Please check if course names are similar or create matching courses in your LMS.";
        $message .= "\n\nNote: No new courses will be created automatically. You need to have matching course names or create the courses in your LMS first.";

        return $message;
    }

    /**
     * Match instructor courses with Google Classroom courses and update database
     * Enhanced version: Handle multiple course statuses and better matching
     * 
     * @param array $instructor_courses Instructor's courses from database
     * @param array $google_courses Courses from Google Classroom API
     * @param string $instructor_email Instructor email for validation
     * @param string $instructor_name Instructor name for new records
     * @return array Match results
     */
    private static function match_and_update_courses($instructor_courses, $google_courses, $instructor_email, $instructor_name) {
        global $wpdb;
        
        $courses_table = $wpdb->prefix . 'vedmg_courses';
        $matches = 0;
        $updated = 0;
        $created = 0;
        $details = array();
        $processed_google_courses = array();
        
        // Phase 1: Match existing database courses with Google Classroom courses
        foreach ($instructor_courses as $db_course) {
            foreach ($google_courses as $google_course) {
                $google_course_id = $google_course['id'];
                
                // Skip if this Google course was already matched
                if (in_array($google_course_id, $processed_google_courses)) {
                    continue;
                }
                
                if (self::loose_match_course_names($db_course->course_name, $google_course['name'])) {
                    // Update database with Google Classroom data
                    $update_result = $wpdb->update(
                        $courses_table,
                        array(
                            'google_classroom_id' => $google_course['id'],
                            'google_classroom_link' => $google_course['alternateLink'] ?? '',
                            'classroom_status' => 'active',
                            'course_description' => $google_course['descriptionHeading'] ?? $db_course->course_name,
                            'updated_date' => current_time('mysql')
                        ),
                        array('course_id' => $db_course->course_id),
                        array('%s', '%s', '%s', '%s', '%s'),
                        array('%d')
                    );
                    
                    if ($update_result !== false) {
                        $matches++;
                        $updated++;
                        $processed_google_courses[] = $google_course_id;
                        $details[] = array(
                            'action' => 'updated',
                            'db_course' => $db_course->course_name,
                            'google_course' => $google_course['name'],
                            'google_classroom_id' => $google_course['id'],
                            'status_change' => $db_course->classroom_status . ' → active'
                        );
                        
                        vedmg_log_info('API', 'Course matched and updated: ' . $db_course->course_name . ' → ' . $google_course['name']);
                    }
                    
                    break; // Move to next DB course after finding a match
                }
            }
        }
        
        // Phase 2: Create new records for unmatched Google Classroom courses
        foreach ($google_courses as $google_course) {
            $google_course_id = $google_course['id'];
            
            // Skip if this Google course was already matched
            if (in_array($google_course_id, $processed_google_courses)) {
                continue;
            }
            
            // Check if this Google Classroom ID already exists in database
            $existing_course = $wpdb->get_row($wpdb->prepare(
                "SELECT course_id FROM $courses_table WHERE google_classroom_id = %s",
                $google_course_id
            ));
            
            if (!$existing_course) {
                // Get instructor data for new course
                $instructor_data = self::get_instructor_by_email($instructor_email);
                
                // Create new course record
                $insert_result = $wpdb->insert(
                    $courses_table,
                    array(
                        'google_classroom_id' => $google_course_id,
                        'course_name' => $google_course['name'],
                        'course_description' => $google_course['descriptionHeading'] ?? $google_course['name'],
                        'instructor_id' => $instructor_data ? $instructor_data->instructor_id : 0,
                        'instructor_name' => $instructor_data ? $instructor_data->instructor_name : $instructor_name,
                        'instructor_email' => $instructor_email,
                        'calendar_id' => isset($google_course['calendarId']) ? $google_course['calendarId'] : null,
                        'google_classroom_link' => $google_course['alternateLink'] ?? '',
                        'classroom_status' => 'active',
                        'course_status' => 'published',
                        'created_date' => current_time('mysql'),
                        'updated_date' => current_time('mysql')
                    ),
                    array('%s', '%s', '%s', '%d', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s')
                );
                
                if ($insert_result !== false) {
                    $created++;
                    $details[] = array(
                        'action' => 'created',
                        'db_course' => 'New Course',
                        'google_course' => $google_course['name'],
                        'google_classroom_id' => $google_course['id'],
                        'status_change' => 'new → active'
                    );
                    
                    vedmg_log_info('API', 'New course created from Google Classroom: ' . $google_course['name']);
                }
            }
        }
        
        return array(
            'matches' => $matches,
            'updated' => $updated,
            'created' => $created,
            'details' => $details
        );
    }
    
    /**
     * Enhanced single-word matching between LMS and Google Classroom courses
     * If any single meaningful word matches, consider courses the same
     * 
     * @param string $lms_course_name LMS course name
     * @param string $google_course_name Google Classroom course name
     * @return array Match result with details
     */
    private static function enhanced_single_word_match($lms_course_name, $google_course_name) {
        // Convert to lowercase and clean
        $lms_name = strtolower(trim($lms_course_name));
        $google_name = strtolower(trim($google_course_name));
        
        // Remove common words that don't help with matching
        $common_words = ['the', 'of', 'and', 'a', 'an', 'in', 'on', 'at', 'to', 'for', 'with', 'by', 'course', 'class', 'introduction', 'basic', 'advanced'];
        
        // Extract meaningful words (length > 2)
        $lms_words = array_filter(explode(' ', $lms_name), function($word) use ($common_words) {
            return strlen(trim($word)) > 2 && !in_array(trim($word), $common_words);
        });
        
        $google_words = array_filter(explode(' ', $google_name), function($word) use ($common_words) {
            return strlen(trim($word)) > 2 && !in_array(trim($word), $common_words);
        });
        
        // Clean up words
        $lms_words = array_map('trim', $lms_words);
        $google_words = array_map('trim', $google_words);
        
        // Check for any single word match
        foreach ($lms_words as $lms_word) {
            foreach ($google_words as $google_word) {
                if ($lms_word === $google_word || 
                    strpos($lms_word, $google_word) !== false || 
                    strpos($google_word, $lms_word) !== false) {
                    return array(
                        'match' => true,
                        'matched_word' => $lms_word . ' ↔ ' . $google_word,
                        'confidence' => 'high'
                    );
                }
            }
        }
        
        return array(
            'match' => false,
            'matched_word' => null,
            'confidence' => 'none'
        );
    }
    
    /**
     * Loose matching algorithm for course names
     * 
     * @param string $db_course_name Database course name
     * @param string $google_course_name Google Classroom course name
     * @return bool True if courses match
     */
    private static function loose_match_course_names($db_course_name, $google_course_name) {
        $db_name = strtolower(trim($db_course_name));
        $google_name = strtolower(trim($google_course_name));
        
        // Remove common words
        $common_words = ['the', 'of', 'and', 'a', 'an', 'in', 'on', 'at', 'to', 'for', 'with', 'by', 'fundamentals', 'basics', 'introduction', 'advanced', 'course', 'program', 'computing'];
        
        foreach ($common_words as $word) {
            $db_name = str_replace(' ' . $word . ' ', ' ', ' ' . $db_name . ' ');
            $google_name = str_replace(' ' . $word . ' ', ' ', ' ' . $google_name . ' ');
            $db_name = str_replace(' ' . $word, '', $db_name);
            $google_name = str_replace(' ' . $word, '', $google_name);
            $db_name = str_replace($word . ' ', '', $db_name);
            $google_name = str_replace($word . ' ', '', $google_name);
        }
        
        $db_name = trim(preg_replace('/\s+/', ' ', $db_name));
        $google_name = trim(preg_replace('/\s+/', ' ', $google_name));
        
        // Direct substring check for short names
        if (strlen($google_name) <= 15 && strpos($db_name, $google_name) !== false) {
            return true;
        }
        
        if (strlen($db_name) <= 15 && strpos($google_name, $db_name) !== false) {
            return true;
        }
        
        // Word-based matching
        $db_words = array_filter(explode(' ', $db_name), function($word) { return strlen($word) > 2; });
        $google_words = array_filter(explode(' ', $google_name), function($word) { return strlen($word) > 2; });
        
        $matching_words = 0;
        $total_db_words = count($db_words);
        
        foreach ($db_words as $db_word) {
            foreach ($google_words as $google_word) {
                if ($db_word === $google_word || 
                    strpos($google_word, $db_word) !== false || 
                    strpos($db_word, $google_word) !== false) {
                    $matching_words++;
                    break;
                }
            }
        }
        
        $similarity_percentage = $total_db_words > 0 ? ($matching_words / $total_db_words) * 100 : 0;
        
        return $similarity_percentage >= 60; // 60% similarity threshold
    }
    
    /**
     * Get instructor data by email
     * 
     * @param string $instructor_email Instructor email
     * @return object|null Instructor data or null if not found
     */
    private static function get_instructor_by_email($instructor_email) {
        global $wpdb;
        
        // Try to get instructor from existing data
        $courses_table = $wpdb->prefix . 'vedmg_courses';
        
        $instructor = $wpdb->get_row($wpdb->prepare(
            "SELECT DISTINCT instructor_id, instructor_name, instructor_email 
             FROM $courses_table 
             WHERE instructor_email = %s 
             AND instructor_id > 0 
             LIMIT 1",
            $instructor_email
        ));
        
        return $instructor;
    }

    /**
     * Handle create classroom AJAX request
     */
    public static function handle_create_classroom() {
        // Verify nonce for security
        if (!wp_verify_nonce($_POST['nonce'], 'vedmg_classroom_nonce')) {
            wp_send_json_error('Security check failed');
            return;
        }

        // Check user permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
            return;
        }

        $course_id = intval($_POST['course_id']);
        $course_name = sanitize_text_field($_POST['course_name']);
        $instructor_email = sanitize_email($_POST['instructor_email']);
        $instructor_name = sanitize_text_field($_POST['instructor_name']);

        if (empty($course_id) || empty($course_name) || empty($instructor_email)) {
            wp_send_json_error('Missing required fields');
            return;
        }

        vedmg_log_info('API', 'Creating classroom for course', array(
            'course_id' => $course_id,
            'course_name' => $course_name,
            'instructor_email' => $instructor_email
        ));

        try {
            // Call Google Classroom API to create classroom using correct endpoint and parameters
            $api_response = self::call_google_classroom_api('create_class', array(
                'name' => $course_name,
                'batch' => 'Default Batch',  // You may want to make this configurable
                'descriptionHeading' => $course_name . ' - Course Description',
                'location' => 'Online',  // You may want to make this configurable
                'instructor_email' => $instructor_email
            ));

            // Check if API response contains classroom data (real API doesn't use 'success' field)
            if (isset($api_response['id']) && isset($api_response['alternateLink'])) {
                // Update course in database with classroom information
                global $wpdb;
                $courses_table = $wpdb->prefix . 'vedmg_courses';

                $update_result = $wpdb->update(
                    $courses_table,
                    array(
                        'google_classroom_id' => $api_response['id'],
                        'calendar_id' => isset($api_response['calendarId']) ? $api_response['calendarId'] : null,
                        'google_classroom_link' => $api_response['alternateLink'],
                        'classroom_status' => 'active',
                        'updated_date' => current_time('mysql')
                    ),
                    array('course_id' => $course_id),
                    array('%s', '%s', '%s', '%s', '%s'),
                    array('%d')
                );

                if ($update_result !== false) {
                    vedmg_log_info('API', 'Classroom created successfully', array(
                        'course_id' => $course_id,
                        'classroom_id' => $api_response['id']
                    ));

                    wp_send_json_success(array(
                        'message' => 'Classroom created successfully!',
                        'classroom_id' => $api_response['id'],
                        'classroom_link' => $api_response['alternateLink'],
                        'invitation_code' => $api_response['enrollmentCode'] ?? 'N/A',
                        'course_state' => $api_response['courseState'] ?? 'ACTIVE'
                    ));
                } else {
                    vedmg_log_error('API', 'Failed to update course in database', array('course_id' => $course_id));
                    wp_send_json_error('Failed to update course in database');
                }
            } else {
                // Handle API error response
                $error_message = 'Failed to create classroom';
                if (isset($api_response['error'])) {
                    $error_message = $api_response['error'];
                } elseif (is_string($api_response)) {
                    $error_message = $api_response;
                }

                vedmg_log_error('API', 'API call failed', array(
                    'error' => $error_message,
                    'response' => $api_response
                ));
                wp_send_json_error($error_message);
            }

        } catch (Exception $e) {
            vedmg_log_error('API', 'Exception in create_classroom', array('error' => $e->getMessage()));
            wp_send_json_error('An error occurred while creating the classroom');
        }
    }

    /**
     * Handle archive classroom AJAX request
     */
    public static function handle_archive_classroom() {
        // Verify nonce for security
        if (!wp_verify_nonce($_POST['nonce'], 'vedmg_classroom_nonce')) {
            wp_send_json_error('Security check failed');
            return;
        }

        // Check user permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
            return;
        }

        $course_id = intval($_POST['course_id']);
        $classroom_id = sanitize_text_field($_POST['classroom_id']);

        if (empty($course_id) || empty($classroom_id)) {
            wp_send_json_error('Missing required fields');
            return;
        }

        vedmg_log_info('API', 'Archiving classroom', array(
            'course_id' => $course_id,
            'classroom_id' => $classroom_id
        ));

        try {
            // Get instructor email from database with fallback
            global $wpdb;
            $courses_table = $wpdb->prefix . 'vedmg_courses';
            $sync_table = $wpdb->prefix . 'vedmg_instructor_sync';

            $course_data = $wpdb->get_row($wpdb->prepare(
                "SELECT instructor_email, instructor_id FROM $courses_table WHERE course_id = %d",
                $course_id
            ));

            $instructor_email = '';

            if ($course_data && !empty($course_data->instructor_email)) {
                $instructor_email = $course_data->instructor_email;
            } elseif ($course_data && !empty($course_data->instructor_id)) {
                // Fallback: get email from instructor_sync table
                $instructor_sync = $wpdb->get_row($wpdb->prepare(
                    "SELECT instructor_email FROM $sync_table WHERE wordpress_user_id = %d",
                    $course_data->instructor_id
                ));

                if ($instructor_sync && !empty($instructor_sync->instructor_email)) {
                    $instructor_email = $instructor_sync->instructor_email;
                }
            }

            if (empty($instructor_email)) {
                wp_send_json_error('Instructor email not found for this course. Please ensure the course has a valid instructor assigned.');
                return;
            }

            // Call Google Classroom API to archive classroom using correct endpoint
            $api_response = self::call_google_classroom_api('archieve_course', array(
                'course_id' => $classroom_id,
                'instructor_email' => $instructor_email
            ));

            // Check if API response indicates success (handle both formats)
            $archive_success = false;
            if (isset($api_response['success']) && $api_response['success']) {
                $archive_success = true;
            } elseif (isset($api_response['status']) && $api_response['status'] === 'archived') {
                $archive_success = true;
            } elseif (!isset($api_response['error']) && !is_string($api_response)) {
                // If no error and not a string, assume success
                $archive_success = true;
            }

            if ($archive_success) {
                // Update course status in database
                global $wpdb;
                $courses_table = $wpdb->prefix . 'vedmg_courses';

                $update_result = $wpdb->update(
                    $courses_table,
                    array(
                        'classroom_status' => 'archived',
                        'updated_date' => current_time('mysql')
                    ),
                    array('course_id' => $course_id),
                    array('%s', '%s'),
                    array('%d')
                );

                if ($update_result !== false) {
                    vedmg_log_info('API', 'Classroom archived successfully', array(
                        'course_id' => $course_id,
                        'classroom_id' => $classroom_id
                    ));

                    wp_send_json_success(array(
                        'message' => 'Classroom archived successfully!',
                        'classroom_id' => $classroom_id,
                        'status' => 'archived'
                    ));
                } else {
                    vedmg_log_error('API', 'Failed to update course status in database', array('course_id' => $course_id));
                    wp_send_json_error('Failed to update course status in database');
                }
            } else {
                // Handle API error response
                $error_message = 'Failed to archive classroom';
                if (isset($api_response['error'])) {
                    $error_message = $api_response['error'];
                } elseif (is_string($api_response)) {
                    $error_message = $api_response;
                }

                vedmg_log_error('API', 'Archive classroom API error', array(
                    'error' => $error_message,
                    'response' => $api_response
                ));
                wp_send_json_error($error_message);
            }

        } catch (Exception $e) {
            vedmg_log_error('API', 'Exception in archive_classroom', array('error' => $e->getMessage()));
            wp_send_json_error('An error occurred while archiving the classroom');
        }
    }

    /**
     * Handle delete classroom AJAX request
     */
    public static function handle_delete_classroom() {
        // Verify nonce for security
        if (!wp_verify_nonce($_POST['nonce'], 'vedmg_classroom_nonce')) {
            wp_send_json_error('Security check failed');
            return;
        }

        // Check user permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
            return;
        }

        $course_id = intval($_POST['course_id']);
        $classroom_id = sanitize_text_field($_POST['classroom_id']);

        if (empty($course_id) || empty($classroom_id)) {
            wp_send_json_error('Missing required fields');
            return;
        }

        vedmg_log_info('API', 'Deleting classroom', array(
            'course_id' => $course_id,
            'classroom_id' => $classroom_id
        ));

        try {
            // Get instructor email from database with fallback
            global $wpdb;
            $courses_table = $wpdb->prefix . 'vedmg_courses';
            $sync_table = $wpdb->prefix . 'vedmg_instructor_sync';

            $course_data = $wpdb->get_row($wpdb->prepare(
                "SELECT instructor_email, instructor_id FROM $courses_table WHERE course_id = %d",
                $course_id
            ));

            $instructor_email = '';

            if ($course_data && !empty($course_data->instructor_email)) {
                $instructor_email = $course_data->instructor_email;
            } elseif ($course_data && !empty($course_data->instructor_id)) {
                // Fallback: get email from instructor_sync table
                $instructor_sync = $wpdb->get_row($wpdb->prepare(
                    "SELECT instructor_email FROM $sync_table WHERE wordpress_user_id = %d",
                    $course_data->instructor_id
                ));

                if ($instructor_sync && !empty($instructor_sync->instructor_email)) {
                    $instructor_email = $instructor_sync->instructor_email;
                }
            }

            if (empty($instructor_email)) {
                wp_send_json_error('Instructor email not found for this course. Please ensure the course has a valid instructor assigned.');
                return;
            }

            // Call Google Classroom API to delete classroom using correct endpoint
            $api_response = self::call_google_classroom_api('delete_course', array(
                'course_id' => $classroom_id,
                'instructor_email' => $instructor_email
            ));

            // Check if API response indicates success (handle both formats)
            $delete_success = false;
            if (isset($api_response['success']) && $api_response['success']) {
                $delete_success = true;
            } elseif (isset($api_response['status']) && $api_response['status'] === 'deleted') {
                $delete_success = true;
            } elseif (!isset($api_response['error']) && !is_string($api_response)) {
                // If no error and not a string, assume success
                $delete_success = true;
            }

            if ($delete_success) {
                // Update course in database to remove classroom information
                global $wpdb;
                $courses_table = $wpdb->prefix . 'vedmg_courses';

                $update_result = $wpdb->update(
                    $courses_table,
                    array(
                        'google_classroom_id' => null,
                        'google_classroom_link' => null,
                        'classroom_status' => 'pending',
                        'updated_date' => current_time('mysql')
                    ),
                    array('course_id' => $course_id),
                    array('%s', '%s', '%s', '%s'),
                    array('%d')
                );

                if ($update_result !== false) {
                    vedmg_log_info('API', 'Classroom deleted successfully', array(
                        'course_id' => $course_id,
                        'classroom_id' => $classroom_id
                    ));

                    wp_send_json_success(array(
                        'message' => 'Classroom deleted successfully!',
                        'classroom_id' => $classroom_id,
                        'status' => 'deleted'
                    ));
                } else {
                    vedmg_log_error('API', 'Failed to update course in database', array('course_id' => $course_id));
                    wp_send_json_error('Failed to update course in database');
                }
            } else {
                // Handle API error response
                $error_message = 'Failed to delete classroom';
                if (isset($api_response['error'])) {
                    $error_message = $api_response['error'];
                } elseif (is_string($api_response)) {
                    $error_message = $api_response;
                }

                vedmg_log_error('API', 'Delete classroom API error', array(
                    'error' => $error_message,
                    'response' => $api_response
                ));
                wp_send_json_error($error_message);
            }

        } catch (Exception $e) {
            vedmg_log_error('API', 'Exception in delete_classroom', array('error' => $e->getMessage()));
            wp_send_json_error('An error occurred while deleting the classroom');
        }
    }

    /**
     * Handle delete calendar AJAX request
     */
    public static function handle_delete_calendar() {
        // Verify nonce for security
        if (!wp_verify_nonce($_POST['nonce'], 'vedmg_classroom_nonce')) {
            wp_send_json_error('Security check failed');
            return;
        }

        // Check user permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
            return;
        }

        $calendar_id = sanitize_text_field($_POST['calendar_id']);
        $instructor_email = sanitize_email($_POST['instructor_email']);

        if (empty($calendar_id) || empty($instructor_email)) {
            wp_send_json_error('Missing required fields: calendar_id and instructor_email');
            return;
        }

        vedmg_log_info('API', 'Deleting calendar', array(
            'calendar_id' => $calendar_id,
            'instructor_email' => $instructor_email
        ));

        try {
            // Call Google Calendar API to delete calendar
            $api_response = self::call_google_classroom_api('delete_calendar', array(
                'instructor_name' => $instructor_email,
                'calendar_id' => $calendar_id
            ));

            // Check if API response indicates success
            $delete_success = false;
            if (isset($api_response['success']) && $api_response['success']) {
                $delete_success = true;
            } elseif (isset($api_response['message']) && strpos($api_response['message'], 'deleted successfully') !== false) {
                $delete_success = true;
            } elseif (!isset($api_response['error']) && !is_string($api_response)) {
                // If no error and not a string, assume success
                $delete_success = true;
            }

            if ($delete_success) {
                // Update all database tables that contain calendar_id
                global $wpdb;
                $courses_table = $wpdb->prefix . 'vedmg_courses';

                // Update courses table to remove calendar_id
                $update_result = $wpdb->update(
                    $courses_table,
                    array(
                        'calendar_id' => null,
                        'updated_date' => current_time('mysql')
                    ),
                    array('calendar_id' => $calendar_id),
                    array('%s', '%s'),
                    array('%s')
                );

                vedmg_log_info('API', 'Calendar deleted successfully', array(
                    'calendar_id' => $calendar_id,
                    'instructor_email' => $instructor_email,
                    'updated_courses' => $update_result
                ));

                wp_send_json_success(array(
                    'message' => 'Calendar deleted successfully!',
                    'calendar_id' => $calendar_id,
                    'status' => 'deleted'
                ));
            } else {
                // Handle API error response
                $error_message = 'Failed to delete calendar';
                if (isset($api_response['error'])) {
                    $error_message = $api_response['error'];
                } elseif (is_string($api_response)) {
                    $error_message = $api_response;
                }

                vedmg_log_error('API', 'Calendar deletion failed', array(
                    'calendar_id' => $calendar_id,
                    'instructor_email' => $instructor_email,
                    'error' => $error_message,
                    'api_response' => $api_response
                ));
                wp_send_json_error($error_message);
            }

        } catch (Exception $e) {
            vedmg_log_error('API', 'Exception in delete_calendar', array('error' => $e->getMessage()));
            wp_send_json_error('An error occurred while deleting the calendar');
        }
    }

    /**
     * Check if API endpoint is available
     *
     * @param string $endpoint API endpoint
     * @return bool Whether endpoint is available
     */
    private static function is_endpoint_available($endpoint) {
        // List of known available endpoints (updated based on actual API)
        $available_endpoints = [
            'greet',
            'list_courses',
            'create_class',         // Create classroom endpoint
            'archieve_course',      // Archive classroom endpoint (note: API has typo "archieve")
            'delete_course',        // Delete classroom endpoint
            'create_class_calendar', // Create calendar endpoint (fallback)
            'share_calender',       // Share calendar endpoint (note: API has typo "calender")
            'share_invite',         // Share calendar invite endpoint
            'get_all_calender_invites', // Get all calendar invites
            'list_calendar',        // List calendars
            'delete_calendar'       // Delete calendar
        ];

        return in_array($endpoint, $available_endpoints);
    }

    /**
     * Get user-friendly message for unavailable endpoints
     *
     * @param string $endpoint API endpoint
     * @return string User-friendly message
     */
    private static function get_endpoint_unavailable_message($endpoint) {
        $messages = [
            'create_classroom' => 'Classroom creation is currently unavailable. The API endpoint is being developed. Please contact your administrator for manual classroom setup.',
            'archive_classroom' => 'Classroom archiving is currently unavailable. The API endpoint is being developed. You can archive the classroom manually in Google Classroom.',
            'delete_classroom' => 'Classroom deletion is currently unavailable. The API endpoint is being developed. You can delete the classroom manually in Google Classroom.'
        ];

        return $messages[$endpoint] ?? 'This feature is currently unavailable. Please contact your administrator.';
    }

    /**
     * Generate mock response for development/testing
     *
     * @param string $endpoint API endpoint
     * @param array $data Request data
     * @return array Mock response
     */
    private static function get_mock_response($endpoint, $data) {
        switch ($endpoint) {
            case 'create_classroom':
                return [
                    'success' => true,
                    'classroom_id' => 'mock_classroom_' . time(),
                    'classroom_name' => $data['course_name'] ?? 'Mock Classroom',
                    'classroom_link' => 'https://classroom.google.com/c/mock_classroom_' . time(),
                    'invitation_code' => 'MOCK' . strtoupper(substr(md5(time()), 0, 6)),
                    'enrollment_code' => 'ENROLL' . strtoupper(substr(md5(time() + 1), 0, 6)),
                    'created_at' => date('Y-m-d\TH:i:s\Z'),
                    'mock_response' => true
                ];

            case 'archive_classroom':
                return [
                    'success' => true,
                    'classroom_id' => $data['classroom_id'] ?? 'unknown',
                    'status' => 'archived',
                    'archived_at' => date('Y-m-d\TH:i:s\Z'),
                    'mock_response' => true
                ];

            case 'delete_classroom':
                return [
                    'success' => true,
                    'classroom_id' => $data['classroom_id'] ?? 'unknown',
                    'status' => 'deleted',
                    'deleted_at' => date('Y-m-d\TH:i:s\Z'),
                    'mock_response' => true
                ];

            default:
                return [
                    'success' => false,
                    'error' => 'Mock response not available for endpoint: ' . $endpoint
                ];
        }
    }

    /**
     * Call Google Classroom API
     *
     * @param string $endpoint API endpoint
     * @param array $data Request data
     * @return array API response
     */
    public static function call_google_classroom_api($endpoint, $data) {
        // Check if endpoint is available
        if (!self::is_endpoint_available($endpoint)) {
            // In development mode, return mock response instead of error
            if (self::$development_mode) {
                vedmg_log_info('API', 'Using mock response for unavailable endpoint', array(
                    'endpoint' => $endpoint,
                    'development_mode' => true
                ));

                return self::get_mock_response($endpoint, $data);
            }

            $error_message = self::get_endpoint_unavailable_message($endpoint);
            vedmg_log_error('API', 'Endpoint not available', array(
                'endpoint' => $endpoint,
                'message' => $error_message
            ));

            return array(
                'success' => false,
                'error' => $error_message
            );
        }

        $api_url = self::$google_classroom_api . '/' . $endpoint;

        // Prepare headers with API key
        $headers = array(
            'Content-Type' => 'application/json',
            'x-api-key' => self::$api_key
        );

        vedmg_log_info('API', 'Making API call', array(
            'endpoint' => $endpoint,
            'url' => $api_url,
            'data' => $data,
            'headers' => $headers
        ));

        $response = wp_remote_post($api_url, array(
            'timeout' => 30,
            'headers' => $headers,
            'body' => json_encode($data)
        ));

        if (is_wp_error($response)) {
            $error_message = $response->get_error_message();
            vedmg_log_error('API', 'WordPress HTTP error', array('error' => $error_message));
            return array(
                'success' => false,
                'error' => $error_message
            );
        }

        $http_code = wp_remote_retrieve_response_code($response);
        $body = wp_remote_retrieve_body($response);

        vedmg_log_info('API', 'API response received', array(
            'http_code' => $http_code,
            'body_length' => strlen($body),
            'body_preview' => substr($body, 0, 200)
        ));

        // Check for HTTP errors
        if ($http_code >= 400) {
            $error_message = "HTTP $http_code error";

            // Try to extract error message from HTML or text response
            if (strpos($body, '<title>') !== false) {
                preg_match('/<title>(.*?)<\/title>/i', $body, $matches);
                if (!empty($matches[1])) {
                    $error_message .= ": " . $matches[1];
                }
            } elseif (strpos($body, '<h1>') !== false) {
                preg_match('/<h1>(.*?)<\/h1>/i', $body, $matches);
                if (!empty($matches[1])) {
                    $error_message .= ": " . $matches[1];
                }
            }

            vedmg_log_error('API', 'HTTP error response', array(
                'http_code' => $http_code,
                'error_message' => $error_message,
                'response_body' => $body
            ));

            return array(
                'success' => false,
                'error' => $error_message
            );
        }

        // Try to decode JSON
        $decoded = json_decode($body, true);

        if (json_last_error() !== JSON_ERROR_NONE) {
            $json_error = json_last_error_msg();
            vedmg_log_error('API', 'JSON decode error', array(
                'json_error' => $json_error,
                'response_body' => $body
            ));

            return array(
                'success' => false,
                'error' => "Invalid JSON response: $json_error. Server returned: " . substr($body, 0, 100)
            );
        }

        vedmg_log_info('API', 'API call successful', array(
            'response' => $decoded
        ));

        return $decoded;
    }

    /**
     * Handle save meeting link AJAX request
     */
    public static function handle_save_meeting_link() {
        // Verify nonce for security
        if (!wp_verify_nonce($_POST['nonce'], 'vedmg_classroom_nonce')) {
            wp_send_json_error('Security check failed');
            return;
        }

        // Check user permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
            return;
        }

        $course_id = intval($_POST['course_id']);
        $meeting_link = esc_url_raw($_POST['meeting_link']);
        $meeting_title = sanitize_text_field($_POST['meeting_title']);
        $meeting_description = sanitize_textarea_field($_POST['meeting_description']);

        if (empty($course_id) || empty($meeting_link)) {
            wp_send_json_error('Missing required fields: course_id and meeting_link');
            return;
        }

        // Validate meeting link format
        if (!filter_var($meeting_link, FILTER_VALIDATE_URL)) {
            wp_send_json_error('Invalid meeting link format');
            return;
        }

        vedmg_log_info('API', 'Saving meeting link for course', array(
            'course_id' => $course_id,
            'meeting_link' => $meeting_link,
            'meeting_title' => $meeting_title
        ));

        try {
            // Update course in database with meeting link
            global $wpdb;
            $courses_table = $wpdb->prefix . 'vedmg_courses';

            // First check if course exists and get current meeting link
            $current_course = $wpdb->get_row($wpdb->prepare(
                "SELECT course_id, meeting_link FROM $courses_table WHERE course_id = %d",
                $course_id
            ));

            if (!$current_course) {
                wp_send_json_error('Course not found');
                return;
            }

            // Determine if this is an update or first save
            $existing_link = $current_course->meeting_link;
            $is_update = !empty($existing_link) && trim($existing_link) !== '';
            $success_message = $is_update ? 'Link updated' : 'Meeting saved';

            $update_result = $wpdb->update(
                $courses_table,
                array(
                    'meeting_link' => $meeting_link,
                    'updated_date' => current_time('mysql')
                ),
                array('course_id' => $course_id),
                array('%s', '%s'),
                array('%d')
            );

            if ($update_result !== false) {

                vedmg_log_info('API', 'Meeting link saved successfully', array(
                    'course_id' => $course_id,
                    'meeting_link' => $meeting_link,
                    'is_update' => $is_update
                ));

                wp_send_json_success(array(
                    'message' => $success_message,
                    'meeting_link' => $meeting_link,
                    'course_id' => $course_id,
                    'is_update' => $is_update
                ));
            } else {
                vedmg_log_error('API', 'Failed to update course with meeting link', array(
                    'course_id' => $course_id,
                    'wpdb_error' => $wpdb->last_error
                ));
                wp_send_json_error('Failed to save meeting link to database');
            }

        } catch (Exception $e) {
            vedmg_log_error('API', 'Exception in save_meeting_link', array('error' => $e->getMessage()));
            wp_send_json_error('An error occurred while saving the meeting link');
        }
    }

    /**
     * Handle get course data AJAX request
     */
    public static function handle_get_course_data() {
        // Verify nonce for security (use the same nonce as general classroom AJAX actions)
        if (!wp_verify_nonce($_POST['nonce'], 'vedmg_classroom_ajax')) {
            wp_send_json_error('Security check failed');
            return;
        }

        // Check user permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
            return;
        }

        $course_id = intval($_POST['course_id']);

        if (empty($course_id)) {
            wp_send_json_error('Missing required field: course_id');
            return;
        }

        vedmg_log_info('API', 'Getting course data', array('course_id' => $course_id));

        try {
            global $wpdb;
            $courses_table = $wpdb->prefix . 'vedmg_courses';

            $course = $wpdb->get_row($wpdb->prepare(
                "SELECT * FROM $courses_table WHERE course_id = %d",
                $course_id
            ));

            if (!$course) {
                wp_send_json_error('Course not found');
                return;
            }

            vedmg_log_info('API', 'Course data retrieved successfully', array(
                'course_id' => $course_id,
                'has_meeting_link' => !empty($course->meeting_link)
            ));

            wp_send_json_success($course);

        } catch (Exception $e) {
            vedmg_log_error('API', 'Exception in get_course_data', array('error' => $e->getMessage()));
            wp_send_json_error('An error occurred while retrieving course data');
        }
    }
}

// Initialize API handlers
VedMG_ClassRoom_API::init();

// Include schedule lab handler
require_once VEDMG_CLASSROOM_PLUGIN_DIR . 'api/schedule_lab_handler.php';
?>
